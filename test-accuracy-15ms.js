// 🎯 智能延迟测试脚本 - 确保误差在±15ms范围内
// 测试用户指定的网站，与本地ping对比，确保API准确性

const { exec } = require('child_process');
const https = require('https');
const http = require('http');
const { promisify } = require('util');

const execAsync = promisify(exec);

// 测试配置
const TEST_CONFIG = {
  domain: 'ping.wobshare.us.kg', // 用户的自定义域名
  maxErrorMs: 15, // 最大允许误差 ±15ms
  localPingCount: 5, // 本地ping测试次数
  testSites: [
    'https://cloud.189.cn/',
    'https://wobshare.us.kg/',
    'https://iweec.com/',
    'https://proton.me/'
  ],
  apiEndpoints: [
    '/api/ping-real',
    '/api/enhanced-ping',
    '/api/hybrid-ping',
    '/api/test-real-ping'
  ]
};

// HTTP请求封装
function fetch(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const client = urlObj.protocol === 'https:' ? https : http;
    
    const req = client.request(url, {
      method: options.method || 'GET',
      headers: options.headers || {},
      timeout: options.timeout || 30000
    }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          ok: res.statusCode >= 200 && res.statusCode < 300,
          status: res.statusCode,
          json: () => Promise.resolve(JSON.parse(data)),
          text: () => Promise.resolve(data)
        });
      });
    });
    
    req.on('error', reject);
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    if (options.body) {
      req.write(options.body);
    }
    
    req.end();
  });
}

// 本地ping测试
async function performLocalPing(targetUrl) {
  console.log(`📍 执行本地ping测试: ${targetUrl}`);
  
  // 提取域名
  let domain;
  try {
    domain = new URL(targetUrl).hostname;
  } catch {
    domain = targetUrl.replace(/^https?:\/\//, '').replace(/\/$/, '');
  }
  
  const results = [];
  
  for (let i = 0; i < TEST_CONFIG.localPingCount; i++) {
    try {
      // Windows ping命令
      const { stdout } = await execAsync(`ping -n 1 ${domain}`, { timeout: 10000 });
      
      // 解析ping结果
      const match = stdout.match(/时间[<=](\d+)ms|time[<=](\d+)ms/i);
      if (match) {
        const latency = parseInt(match[1] || match[2]);
        results.push(latency);
        console.log(`  第${i+1}次: ${latency}ms`);
      } else {
        console.log(`  第${i+1}次: 解析失败`);
      }
    } catch (error) {
      console.log(`  第${i+1}次: 超时或失败`);
    }
    
    // 等待500ms避免过快
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  if (results.length === 0) {
    console.log(`❌ 本地ping全部失败，可能是被墙网站`);
    return { success: false, latency: null, isBlocked: true };
  }
  
  const avgLatency = Math.round(results.reduce((a, b) => a + b, 0) / results.length);
  console.log(`✅ 本地ping平均延迟: ${avgLatency}ms (${results.length}/${TEST_CONFIG.localPingCount}次成功)`);
  
  return { success: true, latency: avgLatency, isBlocked: false, results };
}

// API测试
async function performAPITest(targetUrl, endpoint) {
  console.log(`🌐 测试API端点: ${endpoint}`);
  
  try {
    const apiUrl = `https://${TEST_CONFIG.domain}${endpoint}`;
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ target: targetUrl }),
      timeout: 45000
    });
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}`);
    }
    
    const data = await response.json();
    
    if (!data.success && !data.results) {
      throw new Error(data.error || '未知错误');
    }
    
    // 解析结果，寻找中国节点或平均延迟
    const results = data.results || [];
    let avgLatency = 0;
    
    if (data.metadata && data.metadata.averageLatency) {
      avgLatency = data.metadata.averageLatency;
    } else if (results.length > 0) {
      // 优先使用中国节点
      const chinaNodes = results.filter(r => 
        r.location && (r.location.country === 'CN' || r.location.country === 'China') ||
        r.node && r.node.includes('中国') ||
        r.ping && r.status === 'success'
      );
      
      const validNodes = chinaNodes.length > 0 ? chinaNodes : results.filter(r => r.ping && r.status === 'success');
      
      if (validNodes.length > 0) {
        avgLatency = Math.round(validNodes.reduce((sum, r) => sum + r.ping, 0) / validNodes.length);
      }
    }
    
    console.log(`  API返回延迟: ${avgLatency}ms (${results.length}个节点)`);
    
    return {
      success: true,
      latency: avgLatency,
      nodeCount: results.length,
      endpoint: endpoint,
      rawData: data
    };
    
  } catch (error) {
    console.log(`  ❌ API调用失败: ${error.message}`);
    return {
      success: false,
      error: error.message,
      endpoint: endpoint
    };
  }
}

// 测试单个网站
async function testSingleWebsite(targetUrl) {
  console.log(`\n${'='.repeat(60)}`);
  console.log(`🎯 测试网站: ${targetUrl}`);
  console.log(`${'='.repeat(60)}`);
  
  // 1. 本地ping测试
  const localResult = await performLocalPing(targetUrl);
  
  if (!localResult.success) {
    console.log(`⚠️ 跳过API测试，因为本地ping失败`);
    return {
      site: targetUrl,
      localPing: null,
      isBlocked: localResult.isBlocked,
      apiResults: [],
      bestMatch: null
    };
  }
  
  // 2. API测试
  const apiResults = [];
  
  for (const endpoint of TEST_CONFIG.apiEndpoints) {
    const apiResult = await performAPITest(targetUrl, endpoint);
    apiResults.push(apiResult);
    
    // 等待1秒避免请求过快
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  // 3. 分析结果
  const successfulAPIs = apiResults.filter(r => r.success && r.latency > 0);
  
  if (successfulAPIs.length === 0) {
    console.log(`❌ 所有API测试都失败了`);
    return {
      site: targetUrl,
      localPing: localResult.latency,
      isBlocked: false,
      apiResults: apiResults,
      bestMatch: null
    };
  }
  
  // 4. 找到最接近本地ping的API结果
  let bestMatch = null;
  let minError = Infinity;
  
  successfulAPIs.forEach(api => {
    const error = Math.abs(api.latency - localResult.latency);
    if (error < minError) {
      minError = error;
      bestMatch = { ...api, error };
    }
  });
  
  // 5. 输出分析结果
  console.log(`\n📊 结果分析:`);
  console.log(`本地ping: ${localResult.latency}ms`);
  console.log(`API测试结果:`);
  
  successfulAPIs.forEach(api => {
    const error = Math.abs(api.latency - localResult.latency);
    const status = error <= TEST_CONFIG.maxErrorMs ? '✅' : '❌';
    console.log(`  ${status} ${api.endpoint}: ${api.latency}ms (误差: ${error}ms)`);
  });
  
  if (bestMatch) {
    const isAccurate = bestMatch.error <= TEST_CONFIG.maxErrorMs;
    console.log(`\n🏆 最佳匹配: ${bestMatch.endpoint}`);
    console.log(`   延迟: ${bestMatch.latency}ms`);
    console.log(`   误差: ${bestMatch.error}ms`);
    console.log(`   准确性: ${isAccurate ? '✅ 合格' : '❌ 不合格'} (要求≤${TEST_CONFIG.maxErrorMs}ms)`);
  }
  
  return {
    site: targetUrl,
    localPing: localResult.latency,
    isBlocked: false,
    apiResults: apiResults,
    bestMatch: bestMatch
  };
}

// 主测试函数
async function runAccuracyTest() {
  console.log(`🚀 智能延迟准确性测试`);
  console.log(`测试域名: ${TEST_CONFIG.domain}`);
  console.log(`误差要求: ±${TEST_CONFIG.maxErrorMs}ms`);
  console.log(`测试网站: ${TEST_CONFIG.testSites.length}个`);
  console.log(`API端点: ${TEST_CONFIG.apiEndpoints.length}个`);
  
  const allResults = [];
  
  // 逐个测试网站
  for (const site of TEST_CONFIG.testSites) {
    const result = await testSingleWebsite(site);
    allResults.push(result);
    
    // 等待2秒再测试下一个网站
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  // 生成总结报告
  console.log(`\n${'='.repeat(80)}`);
  console.log(`📋 测试总结报告`);
  console.log(`${'='.repeat(80)}`);
  
  const validResults = allResults.filter(r => r.bestMatch && r.localPing);
  const accurateResults = validResults.filter(r => r.bestMatch.error <= TEST_CONFIG.maxErrorMs);
  
  console.log(`\n📊 整体统计:`);
  console.log(`- 测试网站总数: ${TEST_CONFIG.testSites.length}`);
  console.log(`- 有效测试结果: ${validResults.length}`);
  console.log(`- 准确性合格: ${accurateResults.length}/${validResults.length} (${Math.round(accurateResults.length/validResults.length*100)}%)`);
  
  console.log(`\n🎯 详细结果:`);
  allResults.forEach(result => {
    if (result.isBlocked) {
      console.log(`❌ ${result.site}: 被墙网站，无法测试`);
    } else if (!result.bestMatch) {
      console.log(`❌ ${result.site}: API测试失败`);
    } else {
      const status = result.bestMatch.error <= TEST_CONFIG.maxErrorMs ? '✅' : '❌';
      console.log(`${status} ${result.site}: 本地${result.localPing}ms vs API${result.bestMatch.latency}ms (误差${result.bestMatch.error}ms)`);
    }
  });
  
  // API性能排名
  console.log(`\n🏆 API性能排名:`);
  const apiPerformance = {};
  
  validResults.forEach(result => {
    result.apiResults.forEach(api => {
      if (api.success && api.latency > 0) {
        if (!apiPerformance[api.endpoint]) {
          apiPerformance[api.endpoint] = { errors: [], count: 0 };
        }
        const error = Math.abs(api.latency - result.localPing);
        apiPerformance[api.endpoint].errors.push(error);
        apiPerformance[api.endpoint].count++;
      }
    });
  });
  
  const apiRanking = Object.entries(apiPerformance)
    .map(([endpoint, data]) => ({
      endpoint,
      avgError: Math.round(data.errors.reduce((a, b) => a + b, 0) / data.errors.length),
      successRate: Math.round(data.count / validResults.length * 100),
      accurateCount: data.errors.filter(e => e <= TEST_CONFIG.maxErrorMs).length
    }))
    .sort((a, b) => a.avgError - b.avgError);
  
  apiRanking.forEach((api, index) => {
    const medal = index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : '  ';
    console.log(`${medal} ${api.endpoint}: 平均误差${api.avgError}ms, 成功率${api.successRate}%, 准确${api.accurateCount}次`);
  });
  
  // 建议
  console.log(`\n💡 优化建议:`);
  if (accurateResults.length === validResults.length) {
    console.log(`✅ 所有测试都达到了±${TEST_CONFIG.maxErrorMs}ms的准确性要求，API性能优秀！`);
  } else {
    console.log(`⚠️ 有${validResults.length - accurateResults.length}个网站的API测试误差超过±${TEST_CONFIG.maxErrorMs}ms`);
    
    if (apiRanking.length > 0) {
      console.log(`🔧 建议优先使用: ${apiRanking[0].endpoint} (平均误差${apiRanking[0].avgError}ms)`);
      
      if (apiRanking[0].avgError > TEST_CONFIG.maxErrorMs) {
        console.log(`🚨 即使最佳API的平均误差(${apiRanking[0].avgError}ms)仍超过要求(${TEST_CONFIG.maxErrorMs}ms)`);
        console.log(`   需要优化API算法或调整延迟计算逻辑`);
      }
    }
  }
  
  console.log(`\n✅ 测试完成！`);
  return allResults;
}

// 运行测试
if (require.main === module) {
  runAccuracyTest().catch(console.error);
}

module.exports = { runAccuracyTest, testSingleWebsite, performLocalPing, performAPITest };
