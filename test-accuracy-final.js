// 🎯 最终版智能延迟测试脚本 - 确保误差在±15ms范围内
// 包含Google等被墙网站的超时处理

const { exec } = require('child_process');
const https = require('https');
const { promisify } = require('util');

const execAsync = promisify(exec);

// 简单的fetch实现
function fetch(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    
    const req = https.request(url, {
      method: options.method || 'GET',
      headers: options.headers || {},
      timeout: options.timeout || 30000
    }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          ok: res.statusCode >= 200 && res.statusCode < 300,
          status: res.statusCode,
          json: () => Promise.resolve(JSON.parse(data))
        });
      });
    });
    
    req.on('error', reject);
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    if (options.body) {
      req.write(options.body);
    }
    
    req.end();
  });
}

// 本地ping测试 - 支持超时和被墙网站检测
async function testLocalPing(domain) {
  console.log(`📍 测试本地ping: ${domain}`);
  
  try {
    const { stdout, stderr } = await execAsync(`ping -n 4 ${domain}`, { timeout: 20000 });
    console.log('Ping输出:', stdout);
    
    // 检查是否包含超时或无法访问的信息
    if (stdout.includes('请求超时') || stdout.includes('Request timed out') || 
        stdout.includes('无法访问') || stdout.includes('Destination host unreachable') ||
        stdout.includes('100% 丢失') || stdout.includes('100% loss') ||
        stdout.includes('找不到主机') || stdout.includes('could not find host')) {
      console.log(`❌ 网站无法访问或被墙: ${domain}`);
      return { latency: null, isBlocked: true, status: 'blocked' };
    }
    
    // 尝试多种匹配模式（支持中文和英文）
    const patterns = [
      /时间[<=](\d+)ms/gi,
      /time[<=](\d+)ms/gi,
      /平均 = (\d+)ms/gi,
      /Average = (\d+)ms/gi,
      /ʱ��=(\d+)ms/gi,  // 中文编码问题
      /ƽ�� = (\d+)ms/gi, // 中文编码问题
      /��� = (\d+)ms/gi,  // 最小值
      /��� = (\d+)ms/gi   // 最大值
    ];
    
    const results = [];
    
    // 首先尝试直接从统计信息中提取平均值
    const avgMatch = stdout.match(/ƽ�� = (\d+)ms/i) || stdout.match(/平均 = (\d+)ms/i) || stdout.match(/Average = (\d+)ms/i);
    if (avgMatch) {
      const avg = parseInt(avgMatch[1]);
      if (avg && avg > 0) {
        console.log(`✅ 从统计信息提取平均值: ${avg}ms`);
        return { latency: avg, isBlocked: false, status: 'success' };
      }
    }
    
    // 如果没有统计信息，从每次ping中提取
    for (const pattern of patterns) {
      const matches = [...stdout.matchAll(pattern)];
      matches.forEach(match => {
        const latency = parseInt(match[1]);
        if (latency && latency > 0) {
          results.push(latency);
        }
      });
    }
    
    if (results.length > 0) {
      const avg = Math.round(results.reduce((a, b) => a + b, 0) / results.length);
      console.log(`✅ 本地ping结果: ${avg}ms (${results.length}次测量)`);
      return { latency: avg, isBlocked: false, status: 'success' };
    } else {
      console.log(`❌ 无法解析ping结果，可能是网络问题`);
      return { latency: null, isBlocked: false, status: 'failed' };
    }
    
  } catch (error) {
    console.log(`❌ 本地ping失败: ${error.message}`);
    // 如果是超时错误，很可能是被墙网站
    if (error.message.includes('timeout') || error.message.includes('超时')) {
      return { latency: null, isBlocked: true, status: 'timeout' };
    }
    return { latency: null, isBlocked: false, status: 'error' };
  }
}

// API测试 - 支持多个端点
async function testAPI(targetUrl, apiEndpoint = '/api/real-ping') {
  console.log(`🌐 测试API: ${targetUrl} (使用 ${apiEndpoint})`);
  
  try {
    const apiUrl = `https://ping.wobshare.us.kg${apiEndpoint}`;
    console.log(`调用API: ${apiUrl}`);
    
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: { 
        'Content-Type': 'application/json',
        'User-Agent': 'PingTest/1.0'
      },
      body: JSON.stringify({ target: targetUrl, maxNodes: 10 }),
      timeout: 60000  // 增加超时时间
    });
    
    console.log(`API响应状态: ${response.status}`);
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }
    
    const data = await response.json();
    console.log('API响应数据:', JSON.stringify(data, null, 2));
    
    // 处理真实Globalping API的响应格式
    if (data.results && data.results.length > 0) {
      // 优先使用中国节点
      const chinaNodes = data.results.filter(r => 
        r.location && (
          r.location.country === 'CN' || 
          r.location.country === 'China' ||
          r.location.country === 'HK' ||
          r.location.country === 'TW'
        ) && r.ping && r.status === 'success'
      );
      
      const validNodes = chinaNodes.length > 0 ? chinaNodes : 
                        data.results.filter(r => r.ping && r.status === 'success');
      
      if (validNodes.length > 0) {
        const avg = Math.round(validNodes.reduce((sum, r) => sum + r.ping, 0) / validNodes.length);
        console.log(`✅ API延迟: ${avg}ms (${validNodes.length}个有效节点，${chinaNodes.length}个中国节点)`);
        return { 
          latency: avg, 
          nodeCount: validNodes.length, 
          chinaNodes: chinaNodes.length,
          isBlocked: avg > 500 // 如果延迟超过500ms，可能是被墙
        };
      }
    }
    
    // 检查metadata中的平均延迟
    if (data.metadata && data.metadata.averageLatency) {
      console.log(`✅ API延迟 (metadata): ${data.metadata.averageLatency}ms`);
      return { 
        latency: data.metadata.averageLatency, 
        nodeCount: data.results?.length || 0, 
        chinaNodes: 0,
        isBlocked: data.metadata.averageLatency > 500
      };
    }
    
    console.log(`❌ 无法从API响应中提取延迟数据`);
    return null;
    
  } catch (error) {
    console.log(`❌ API调用失败: ${error.message}`);
    return null;
  }
}

// 测试单个网站
async function testSite(siteUrl) {
  console.log(`\n${'='.repeat(60)}`);
  console.log(`🎯 测试网站: ${siteUrl}`);
  console.log(`${'='.repeat(60)}`);
  
  // 提取域名
  let domain;
  try {
    domain = new URL(siteUrl).hostname;
  } catch {
    domain = siteUrl.replace(/^https?:\/\//, '').replace(/\/$/, '');
  }
  
  console.log(`域名: ${domain}`);
  
  // 本地ping测试
  const localResult = await testLocalPing(domain);
  
  if (localResult.isBlocked) {
    console.log(`⚠️ 本地ping显示网站被墙，测试API是否能正确识别`);
  } else if (!localResult.latency) {
    console.log(`⚠️ 本地ping失败，但仍会测试API`);
  }
  
  // 测试多个API端点
  const apiEndpoints = [
    '/api/real-ping',        // 真实Globalping API
    '/api/ping-globalping',  // Globalping API
    '/api/enhanced-ping',    // 混合策略API (作为对比)
  ];
  
  const apiResults = [];
  
  for (const endpoint of apiEndpoints) {
    console.log(`\n🔍 测试API端点: ${endpoint}`);
    const apiResult = await testAPI(siteUrl, endpoint);
    
    if (apiResult) {
      let error = null;
      let isAccurate = false;
      
      if (localResult.isBlocked) {
        // 对于被墙网站，API应该返回高延迟或识别为被墙
        isAccurate = apiResult.isBlocked || apiResult.latency > 300;
        error = isAccurate ? 0 : Math.abs(apiResult.latency - 300); // 假设被墙网站应该>300ms
      } else if (localResult.latency) {
        // 对于正常网站，计算误差
        error = Math.abs(apiResult.latency - localResult.latency);
        isAccurate = error <= 15;
      }
      
      apiResults.push({
        endpoint,
        latency: apiResult.latency,
        nodeCount: apiResult.nodeCount,
        chinaNodes: apiResult.chinaNodes,
        isBlocked: apiResult.isBlocked,
        error,
        isAccurate
      });
      
      if (localResult.isBlocked) {
        console.log(`  结果: ${apiResult.latency}ms ${apiResult.isBlocked ? '(识别为被墙)' : ''} ${isAccurate ? '✅' : '❌'}`);
      } else {
        console.log(`  结果: ${apiResult.latency}ms (误差: ${error}ms) ${isAccurate ? '✅' : '❌'}`);
      }
    } else {
      apiResults.push({
        endpoint,
        latency: null,
        error: null,
        isAccurate: false
      });
      console.log(`  结果: 失败 ❌`);
    }
    
    // 等待2秒避免请求过快
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  // 找到最准确的API
  const validResults = apiResults.filter(r => r.latency !== null);
  let bestMatch = null;
  
  if (validResults.length > 0) {
    if (localResult.isBlocked) {
      // 对于被墙网站，选择能正确识别的API
      bestMatch = validResults.find(r => r.isAccurate) || validResults[0];
    } else {
      // 对于正常网站，选择误差最小的API
      bestMatch = validResults.reduce((best, current) => 
        (current.error !== null && (best.error === null || current.error < best.error)) ? current : best
      );
    }
  }
  
  // 输出对比结果
  console.log(`\n📊 结果对比:`);
  if (localResult.isBlocked) {
    console.log(`本地ping: 被墙/超时`);
  } else {
    console.log(`本地ping: ${localResult.latency || '失败'}ms`);
  }
  
  apiResults.forEach(result => {
    if (result.latency) {
      const status = result.isAccurate ? '✅' : '❌';
      if (localResult.isBlocked) {
        console.log(`${status} ${result.endpoint}: ${result.latency}ms ${result.isBlocked ? '(识别为被墙)' : ''}`);
      } else {
        console.log(`${status} ${result.endpoint}: ${result.latency}ms (误差: ${result.error}ms)`);
      }
    } else {
      console.log(`❌ ${result.endpoint}: 失败`);
    }
  });
  
  if (bestMatch) {
    console.log(`\n🏆 最佳匹配: ${bestMatch.endpoint}`);
    if (localResult.isBlocked) {
      console.log(`   延迟: ${bestMatch.latency}ms ${bestMatch.isBlocked ? '(正确识别被墙)' : '(未识别被墙)'}`);
    } else {
      console.log(`   延迟: ${bestMatch.latency}ms (误差: ${bestMatch.error}ms)`);
    }
  }
  
  return { 
    site: siteUrl, 
    localResult, 
    apiResults, 
    bestMatch 
  };
}

// 主测试函数
async function runTest() {
  console.log(`🚀 开始智能延迟准确性测试`);
  console.log(`目标误差: ±15ms (正常网站) | 正确识别被墙网站`);
  
  const testSites = [
    'https://cloud.189.cn/',
    'https://wobshare.us.kg/',
    'https://iweec.com/',
    'https://proton.me/',
    'https://www.google.com/'  // 添加Google作为被墙网站测试
  ];
  
  const results = [];
  
  for (const site of testSites) {
    const result = await testSite(site);
    results.push(result);
    
    // 等待3秒再测试下一个网站
    await new Promise(resolve => setTimeout(resolve, 3000));
  }
  
  // 生成总结报告
  console.log(`\n${'='.repeat(80)}`);
  console.log(`📋 测试总结报告`);
  console.log(`${'='.repeat(80)}`);
  
  const normalSites = results.filter(r => !r.localResult.isBlocked && r.localResult.latency);
  const blockedSites = results.filter(r => r.localResult.isBlocked);
  const failedSites = results.filter(r => !r.localResult.isBlocked && !r.localResult.latency);
  
  console.log(`\n📊 网站分类:`);
  console.log(`- 正常网站: ${normalSites.length}个`);
  console.log(`- 被墙网站: ${blockedSites.length}个`);
  console.log(`- 测试失败: ${failedSites.length}个`);
  
  // 分析正常网站的准确性
  if (normalSites.length > 0) {
    console.log(`\n🎯 正常网站准确性分析:`);
    normalSites.forEach(result => {
      const bestAPI = result.bestMatch;
      if (bestAPI && bestAPI.error !== null) {
        const status = bestAPI.isAccurate ? '✅' : '❌';
        console.log(`${status} ${result.site}: 本地${result.localResult.latency}ms vs API${bestAPI.latency}ms (误差${bestAPI.error}ms)`);
      } else {
        console.log(`❌ ${result.site}: API测试失败`);
      }
    });
  }
  
  // 分析被墙网站的识别能力
  if (blockedSites.length > 0) {
    console.log(`\n🚫 被墙网站识别分析:`);
    blockedSites.forEach(result => {
      const bestAPI = result.bestMatch;
      if (bestAPI) {
        const status = bestAPI.isAccurate ? '✅' : '❌';
        console.log(`${status} ${result.site}: ${bestAPI.isBlocked ? '正确识别为被墙' : `延迟${bestAPI.latency}ms (应>300ms)`}`);
      } else {
        console.log(`❌ ${result.site}: API测试失败`);
      }
    });
  }
  
  // API性能排名
  console.log(`\n🏆 API性能排名:`);
  const apiPerformance = {};
  
  results.forEach(result => {
    result.apiResults.forEach(api => {
      if (api.latency !== null) {
        if (!apiPerformance[api.endpoint]) {
          apiPerformance[api.endpoint] = { 
            accurateCount: 0, 
            totalCount: 0,
            errors: []
          };
        }
        apiPerformance[api.endpoint].totalCount++;
        if (api.isAccurate) {
          apiPerformance[api.endpoint].accurateCount++;
        }
        if (api.error !== null) {
          apiPerformance[api.endpoint].errors.push(api.error);
        }
      }
    });
  });
  
  const apiRanking = Object.entries(apiPerformance)
    .map(([endpoint, data]) => ({
      endpoint,
      accuracyRate: Math.round(data.accurateCount / data.totalCount * 100),
      avgError: data.errors.length > 0 ? Math.round(data.errors.reduce((a, b) => a + b, 0) / data.errors.length) : 0,
      successCount: data.totalCount
    }))
    .sort((a, b) => b.accuracyRate - a.accuracyRate);
  
  apiRanking.forEach((api, index) => {
    const medal = index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : '  ';
    console.log(`${medal} ${api.endpoint}: 准确率${api.accuracyRate}%, 平均误差${api.avgError}ms, 成功${api.successCount}次`);
  });
  
  // 最终建议
  console.log(`\n💡 最终评估:`);
  const totalTests = normalSites.length + blockedSites.length;
  const accurateTests = results.filter(r => r.bestMatch && r.bestMatch.isAccurate).length;
  const overallAccuracy = totalTests > 0 ? Math.round(accurateTests / totalTests * 100) : 0;
  
  console.log(`- 总体准确率: ${overallAccuracy}% (${accurateTests}/${totalTests})`);
  
  if (overallAccuracy >= 80) {
    console.log(`✅ API性能优秀，满足±15ms准确性要求！`);
  } else if (overallAccuracy >= 60) {
    console.log(`⚠️ API性能良好，但仍有改进空间`);
  } else {
    console.log(`❌ API性能需要优化，建议调整算法`);
  }
  
  if (apiRanking.length > 0) {
    console.log(`🔧 推荐使用: ${apiRanking[0].endpoint} (准确率${apiRanking[0].accuracyRate}%)`);
  }
  
  console.log(`\n✅ 测试完成！`);
  return results;
}

runTest().catch(console.error);
