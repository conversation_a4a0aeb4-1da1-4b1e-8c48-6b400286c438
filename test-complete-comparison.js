// 🎯 完整的本地ping vs API对比测试
const { exec } = require('child_process');
const https = require('https');
const { promisify } = require('util');

const execAsync = promisify(exec);

// 简单的fetch实现
function fetch(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    
    const req = https.request(url, {
      method: options.method || 'GET',
      headers: options.headers || {},
      timeout: options.timeout || 30000
    }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          ok: res.statusCode >= 200 && res.statusCode < 300,
          status: res.statusCode,
          json: () => Promise.resolve(JSON.parse(data)),
          text: () => Promise.resolve(data)
        });
      });
    });
    
    req.on('error', reject);
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    if (options.body) {
      req.write(options.body);
    }
    
    req.end();
  });
}

// 本地ping测试 - 完整处理所有情况
async function performLocalPing(domain) {
  console.log(`📍 执行本地ping测试: ${domain}`);
  
  try {
    const { stdout, stderr } = await execAsync(`ping -n 4 ${domain}`, { timeout: 25000 });
    console.log(`本地ping输出:\n${stdout}`);
    
    // 检查各种失败情况
    if (stdout.includes('请求超时') || stdout.includes('Request timed out')) {
      console.log(`❌ 本地ping: 请求超时 (被墙网站)`);
      return { status: 'timeout', latency: null, isBlocked: true, reason: '请求超时' };
    }
    
    if (stdout.includes('无法访问目标主机') || stdout.includes('Destination host unreachable')) {
      console.log(`❌ 本地ping: 无法访问目标主机`);
      return { status: 'unreachable', latency: null, isBlocked: true, reason: '无法访问目标主机' };
    }
    
    if (stdout.includes('找不到主机') || stdout.includes('could not find host') || stdout.includes('Ping 请求找不到主机')) {
      console.log(`❌ 本地ping: 找不到主机`);
      return { status: 'host_not_found', latency: null, isBlocked: true, reason: '找不到主机' };
    }
    
    if (stdout.includes('100% 丢失') || stdout.includes('100% loss')) {
      console.log(`❌ 本地ping: 100%丢包`);
      return { status: 'packet_loss', latency: null, isBlocked: true, reason: '100%丢包' };
    }
    
    // 尝试提取延迟数据
    // 首先尝试从统计信息提取平均值
    const avgPatterns = [
      /平均 = (\d+)ms/i,
      /Average = (\d+)ms/i,
      /ƽ�� = (\d+)ms/i
    ];
    
    for (const pattern of avgPatterns) {
      const match = stdout.match(pattern);
      if (match) {
        const avg = parseInt(match[1]);
        if (avg && avg > 0) {
          console.log(`✅ 本地ping: ${avg}ms (从统计信息提取)`);
          return { status: 'success', latency: avg, isBlocked: false, reason: '统计平均值' };
        }
      }
    }
    
    // 如果没有统计信息，从每次ping中提取
    const timePatterns = [
      /时间[<=](\d+)ms/gi,
      /time[<=](\d+)ms/gi,
      /ʱ��=(\d+)ms/gi
    ];
    
    const latencies = [];
    for (const pattern of timePatterns) {
      const matches = [...stdout.matchAll(pattern)];
      matches.forEach(match => {
        const latency = parseInt(match[1]);
        if (latency && latency > 0) {
          latencies.push(latency);
        }
      });
    }
    
    if (latencies.length > 0) {
      const avg = Math.round(latencies.reduce((a, b) => a + b, 0) / latencies.length);
      console.log(`✅ 本地ping: ${avg}ms (从${latencies.length}次测量计算平均值: [${latencies.join(', ')}])`);
      return { status: 'success', latency: avg, isBlocked: false, reason: `${latencies.length}次测量平均` };
    }
    
    console.log(`❌ 本地ping: 无法解析延迟数据`);
    return { status: 'parse_failed', latency: null, isBlocked: false, reason: '无法解析延迟' };
    
  } catch (error) {
    console.log(`❌ 本地ping执行失败: ${error.message}`);
    if (error.message.includes('timeout') || error.message.includes('超时')) {
      return { status: 'command_timeout', latency: null, isBlocked: true, reason: '命令执行超时' };
    }
    return { status: 'command_error', latency: null, isBlocked: false, reason: `执行错误: ${error.message}` };
  }
}

// API测试
async function performAPITest(targetUrl) {
  console.log(`🌐 执行API测试: ${targetUrl}`);
  
  try {
    const apiUrl = `https://ping.wobshare.us.kg/api/ping-globalping`;
    
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: { 
        'Content-Type': 'application/json',
        'User-Agent': 'PingTest/1.0'
      },
      body: JSON.stringify({ target: targetUrl, maxNodes: 10 }),
      timeout: 60000
    });
    
    console.log(`API响应状态: ${response.status}`);
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }
    
    const data = await response.json();
    console.log(`API返回数据: ${JSON.stringify(data, null, 2)}`);
    
    if (!data.success || !data.results || data.results.length === 0) {
      console.log(`❌ API测试失败: 无有效结果`);
      return { status: 'no_results', latency: null, nodeCount: 0, reason: 'API无有效结果' };
    }
    
    // 优先使用中国节点
    const chinaNodes = data.results.filter(r => 
      r.location && (
        r.location.country === 'CN' || 
        r.location.country === 'China' ||
        r.location.country === 'HK' ||
        r.location.country === 'TW'
      ) && r.ping && r.status === 'success' && r.ping < 999
    );
    
    const validNodes = chinaNodes.length > 0 ? chinaNodes : 
                      data.results.filter(r => r.ping && r.status === 'success' && r.ping < 999);
    
    if (validNodes.length === 0) {
      // 检查是否所有节点都超时或被墙
      const timeoutNodes = data.results.filter(r => r.status === 'timeout' || r.ping >= 999);
      if (timeoutNodes.length > 0) {
        console.log(`❌ API测试: 所有节点超时或被墙 (${timeoutNodes.length}个节点)`);
        return { status: 'blocked', latency: null, nodeCount: data.results.length, reason: '所有节点超时' };
      }
      
      console.log(`❌ API测试: 无有效节点`);
      return { status: 'no_valid_nodes', latency: null, nodeCount: data.results.length, reason: '无有效节点' };
    }
    
    const avgLatency = Math.round(validNodes.reduce((sum, r) => sum + r.ping, 0) / validNodes.length);
    const realLatencyCount = validNodes.filter(r => r.realLatencyExtracted).length;
    
    console.log(`✅ API测试: ${avgLatency}ms (${validNodes.length}个有效节点，${chinaNodes.length}个中国节点)`);
    console.log(`📊 真实延迟提取: ${realLatencyCount}/${validNodes.length}个节点`);
    
    // 判断是否为被墙网站（API延迟过高）
    const isAPIBlocked = avgLatency > 500;
    
    return { 
      status: 'success', 
      latency: avgLatency, 
      nodeCount: validNodes.length,
      chinaNodes: chinaNodes.length,
      realLatencyCount: realLatencyCount,
      isBlocked: isAPIBlocked,
      reason: `${validNodes.length}个节点平均值`
    };
    
  } catch (error) {
    console.log(`❌ API调用失败: ${error.message}`);
    return { status: 'api_error', latency: null, nodeCount: 0, reason: `API错误: ${error.message}` };
  }
}

// 对比分析
function analyzeComparison(localResult, apiResult, siteUrl) {
  console.log(`\n📊 对比分析: ${siteUrl}`);
  console.log(`本地结果: ${localResult.status} - ${localResult.latency ? localResult.latency + 'ms' : '无延迟'} (${localResult.reason})`);
  console.log(`API结果: ${apiResult.status} - ${apiResult.latency ? apiResult.latency + 'ms' : '无延迟'} (${apiResult.reason})`);
  
  // 情况1: 本地被墙/超时
  if (localResult.isBlocked) {
    if (apiResult.isBlocked || apiResult.status === 'blocked' || apiResult.latency === null) {
      console.log(`✅ 一致性: 都识别为被墙/无法访问`);
      return { 
        isConsistent: true, 
        error: 0, 
        isAccurate: true, 
        category: 'blocked_consistent',
        message: '都正确识别为被墙网站'
      };
    } else {
      console.log(`❌ 不一致: 本地被墙，但API返回${apiResult.latency}ms`);
      return { 
        isConsistent: false, 
        error: null, 
        isAccurate: false, 
        category: 'blocked_inconsistent',
        message: `本地被墙，API却返回${apiResult.latency}ms`
      };
    }
  }
  
  // 情况2: 本地失败
  if (localResult.status !== 'success') {
    console.log(`⚠️ 本地测试失败，无法对比: ${localResult.reason}`);
    return { 
      isConsistent: false, 
      error: null, 
      isAccurate: false, 
      category: 'local_failed',
      message: `本地测试失败: ${localResult.reason}`
    };
  }
  
  // 情况3: API失败
  if (apiResult.status !== 'success') {
    console.log(`❌ API测试失败，无法对比: ${apiResult.reason}`);
    return { 
      isConsistent: false, 
      error: null, 
      isAccurate: false, 
      category: 'api_failed',
      message: `API测试失败: ${apiResult.reason}`
    };
  }
  
  // 情况4: 都成功，计算误差
  const error = Math.abs(apiResult.latency - localResult.latency);
  const isAccurate = error <= 15;
  
  console.log(`📏 延迟误差: ${error}ms`);
  console.log(`🎯 准确性: ${isAccurate ? '✅ 合格' : '❌ 不合格'} (要求≤15ms)`);
  
  return { 
    isConsistent: true, 
    error: error, 
    isAccurate: isAccurate, 
    category: 'normal_comparison',
    message: `误差${error}ms ${isAccurate ? '(合格)' : '(超出±15ms要求)'}`
  };
}

// 测试单个网站
async function testSingleSite(siteUrl) {
  console.log(`\n${'='.repeat(80)}`);
  console.log(`🎯 测试网站: ${siteUrl}`);
  console.log(`${'='.repeat(80)}`);
  
  // 提取域名
  let domain;
  try {
    domain = new URL(siteUrl).hostname;
  } catch {
    domain = siteUrl.replace(/^https?:\/\//, '').replace(/\/$/, '');
  }
  
  console.log(`域名: ${domain}`);
  
  // 1. 本地ping测试
  const localResult = await performLocalPing(domain);
  
  // 等待2秒
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // 2. API测试
  const apiResult = await performAPITest(siteUrl);
  
  // 3. 对比分析
  const comparison = analyzeComparison(localResult, apiResult, siteUrl);
  
  return {
    site: siteUrl,
    domain: domain,
    localResult: localResult,
    apiResult: apiResult,
    comparison: comparison
  };
}

// 主测试函数
async function runCompleteTest() {
  console.log(`🚀 开始完整的本地ping vs API对比测试`);
  console.log(`测试要求: ±15ms误差 | 正确识别被墙网站`);
  console.log(`测试域名: ping.wobshare.us.kg`);
  
  const testSites = [
    'https://cloud.189.cn/',
    'https://wobshare.us.kg/',
    'https://iweec.com/',
    'https://proton.me/',
    'https://www.google.com/'
  ];
  
  const results = [];
  
  for (const site of testSites) {
    const result = await testSingleSite(site);
    results.push(result);
    
    // 等待3秒再测试下一个网站
    await new Promise(resolve => setTimeout(resolve, 3000));
  }
  
  // 生成详细报告
  console.log(`\n${'='.repeat(100)}`);
  console.log(`📋 完整测试报告`);
  console.log(`${'='.repeat(100)}`);
  
  // 分类统计
  const categories = {
    normal_comparison: results.filter(r => r.comparison.category === 'normal_comparison'),
    blocked_consistent: results.filter(r => r.comparison.category === 'blocked_consistent'),
    blocked_inconsistent: results.filter(r => r.comparison.category === 'blocked_inconsistent'),
    local_failed: results.filter(r => r.comparison.category === 'local_failed'),
    api_failed: results.filter(r => r.comparison.category === 'api_failed')
  };
  
  console.log(`\n📊 测试分类统计:`);
  console.log(`- 正常对比: ${categories.normal_comparison.length}个`);
  console.log(`- 被墙一致: ${categories.blocked_consistent.length}个`);
  console.log(`- 被墙不一致: ${categories.blocked_inconsistent.length}个`);
  console.log(`- 本地失败: ${categories.local_failed.length}个`);
  console.log(`- API失败: ${categories.api_failed.length}个`);
  
  // 准确性分析
  const accurateResults = results.filter(r => r.comparison.isAccurate);
  const validComparisons = results.filter(r => r.comparison.isConsistent && r.comparison.category !== 'local_failed' && r.comparison.category !== 'api_failed');
  
  console.log(`\n🎯 准确性分析:`);
  console.log(`- 总测试网站: ${testSites.length}个`);
  console.log(`- 有效对比: ${validComparisons.length}个`);
  console.log(`- 准确结果: ${accurateResults.length}个`);
  console.log(`- 准确率: ${validComparisons.length > 0 ? Math.round(accurateResults.length / validComparisons.length * 100) : 0}%`);
  
  console.log(`\n🎯 详细结果:`);
  results.forEach(result => {
    const status = result.comparison.isAccurate ? '✅' : '❌';
    console.log(`${status} ${result.site}:`);
    console.log(`   本地: ${result.localResult.latency ? result.localResult.latency + 'ms' : result.localResult.status} (${result.localResult.reason})`);
    console.log(`   API: ${result.apiResult.latency ? result.apiResult.latency + 'ms' : result.apiResult.status} (${result.apiResult.reason})`);
    console.log(`   结果: ${result.comparison.message}`);
    
    if (result.apiResult.realLatencyCount !== undefined) {
      console.log(`   真实延迟提取: ${result.apiResult.realLatencyCount}/${result.apiResult.nodeCount}个节点`);
    }
  });
  
  // 最终评估
  console.log(`\n💡 最终评估:`);
  const overallAccuracy = validComparisons.length > 0 ? Math.round(accurateResults.length / validComparisons.length * 100) : 0;
  
  if (overallAccuracy >= 80) {
    console.log(`✅ API性能优秀，满足±15ms准确性要求！`);
  } else if (overallAccuracy >= 60) {
    console.log(`⚠️ API性能良好，但仍有改进空间`);
  } else {
    console.log(`❌ API性能需要进一步优化`);
  }
  
  // 真实延迟提取统计
  const totalNodes = results.reduce((sum, r) => sum + (r.apiResult.nodeCount || 0), 0);
  const realLatencyNodes = results.reduce((sum, r) => sum + (r.apiResult.realLatencyCount || 0), 0);
  const realLatencyRate = totalNodes > 0 ? Math.round(realLatencyNodes / totalNodes * 100) : 0;
  
  console.log(`📊 真实延迟提取率: ${realLatencyRate}% (${realLatencyNodes}/${totalNodes}个节点)`);
  
  console.log(`\n✅ 完整测试完成！`);
  return results;
}

runCompleteTest().catch(console.error);
