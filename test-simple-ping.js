// 简单的ping测试脚本
const { exec } = require('child_process');
const https = require('https');
const { promisify } = require('util');

const execAsync = promisify(exec);

// 简单的fetch实现
function fetch(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    
    const req = https.request(url, {
      method: options.method || 'GET',
      headers: options.headers || {},
      timeout: options.timeout || 30000
    }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          ok: res.statusCode >= 200 && res.statusCode < 300,
          status: res.statusCode,
          json: () => Promise.resolve(JSON.parse(data))
        });
      });
    });
    
    req.on('error', reject);
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    if (options.body) {
      req.write(options.body);
    }
    
    req.end();
  });
}

async function testLocalPing(domain) {
  console.log(`📍 测试本地ping: ${domain}`);
  
  try {
    const { stdout } = await execAsync(`ping -n 3 ${domain}`, { timeout: 15000 });
    console.log('Ping输出:', stdout);
    
    // 尝试多种匹配模式（支持中文和英文）
    const patterns = [
      /时间[<=](\d+)ms/gi,
      /time[<=](\d+)ms/gi,
      /平均 = (\d+)ms/gi,
      /Average = (\d+)ms/gi,
      /ʱ��=(\d+)ms/gi,  // 中文编码问题
      /ƽ�� = (\d+)ms/gi, // 中文编码问题
      /��� = (\d+)ms/gi,  // 最小值
      /��� = (\d+)ms/gi   // 最大值
    ];

    const results = [];

    // 首先尝试直接从统计信息中提取平均值
    const avgMatch = stdout.match(/ƽ�� = (\d+)ms/i) || stdout.match(/平均 = (\d+)ms/i) || stdout.match(/Average = (\d+)ms/i);
    if (avgMatch) {
      const avg = parseInt(avgMatch[1]);
      if (avg && avg > 0) {
        console.log(`✅ 从统计信息提取平均值: ${avg}ms`);
        return avg;
      }
    }

    // 如果没有统计信息，从每次ping中提取
    for (const pattern of patterns) {
      const matches = [...stdout.matchAll(pattern)];
      matches.forEach(match => {
        const latency = parseInt(match[1]);
        if (latency && latency > 0) {
          results.push(latency);
        }
      });
    }
    
    if (results.length > 0) {
      const avg = Math.round(results.reduce((a, b) => a + b, 0) / results.length);
      console.log(`✅ 本地ping结果: ${avg}ms (${results.length}次测量)`);
      return avg;
    } else {
      console.log(`❌ 无法解析ping结果`);
      return null;
    }
    
  } catch (error) {
    console.log(`❌ 本地ping失败: ${error.message}`);
    return null;
  }
}

async function testAPI(targetUrl, apiEndpoint = '/api/real-ping') {
  console.log(`🌐 测试API: ${targetUrl} (使用 ${apiEndpoint})`);

  try {
    const apiUrl = `https://ping.wobshare.us.kg${apiEndpoint}`;
    console.log(`调用API: ${apiUrl}`);

    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'PingTest/1.0'
      },
      body: JSON.stringify({ target: targetUrl, maxNodes: 10 }),
      timeout: 60000  // 增加超时时间，因为真实API需要更长时间
    });

    console.log(`API响应状态: ${response.status}`);

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }

    const data = await response.json();
    console.log('API响应数据:', JSON.stringify(data, null, 2));

    // 处理真实Globalping API的响应格式
    if (data.results && data.results.length > 0) {
      // 优先使用中国节点
      const chinaNodes = data.results.filter(r =>
        r.location && (
          r.location.country === 'CN' ||
          r.location.country === 'China' ||
          r.location.country === 'HK' ||
          r.location.country === 'TW'
        ) && r.ping && r.status === 'success'
      );

      const validNodes = chinaNodes.length > 0 ? chinaNodes :
                        data.results.filter(r => r.ping && r.status === 'success');

      if (validNodes.length > 0) {
        const avg = Math.round(validNodes.reduce((sum, r) => sum + r.ping, 0) / validNodes.length);
        console.log(`✅ API延迟: ${avg}ms (${validNodes.length}个有效节点，${chinaNodes.length}个中国节点)`);
        return { latency: avg, nodeCount: validNodes.length, chinaNodes: chinaNodes.length };
      }
    }

    // 检查metadata中的平均延迟
    if (data.metadata && data.metadata.averageLatency) {
      console.log(`✅ API延迟 (metadata): ${data.metadata.averageLatency}ms`);
      return { latency: data.metadata.averageLatency, nodeCount: data.results?.length || 0, chinaNodes: 0 };
    }

    console.log(`❌ 无法从API响应中提取延迟数据`);
    return null;

  } catch (error) {
    console.log(`❌ API调用失败: ${error.message}`);
    return null;
  }
}

async function testSite(siteUrl) {
  console.log(`\n${'='.repeat(60)}`);
  console.log(`🎯 测试网站: ${siteUrl}`);
  console.log(`${'='.repeat(60)}`);

  // 提取域名
  let domain;
  try {
    domain = new URL(siteUrl).hostname;
  } catch {
    domain = siteUrl.replace(/^https?:\/\//, '').replace(/\/$/, '');
  }

  console.log(`域名: ${domain}`);

  // 本地ping测试
  const localPing = await testLocalPing(domain);

  if (!localPing) {
    console.log(`⚠️ 本地ping失败，跳过API测试`);
    return { site: siteUrl, localPing: null, apiResults: [], bestMatch: null };
  }

  // 测试多个API端点
  const apiEndpoints = [
    '/api/real-ping',        // 真实Globalping API
    '/api/ping-globalping',  // Globalping API
    '/api/enhanced-ping',    // 混合策略API (作为对比)
  ];

  const apiResults = [];

  for (const endpoint of apiEndpoints) {
    console.log(`\n🔍 测试API端点: ${endpoint}`);
    const apiResult = await testAPI(siteUrl, endpoint);

    if (apiResult) {
      const error = Math.abs(apiResult.latency - localPing);
      const isAccurate = error <= 15;

      apiResults.push({
        endpoint,
        latency: apiResult.latency,
        nodeCount: apiResult.nodeCount,
        chinaNodes: apiResult.chinaNodes,
        error,
        isAccurate
      });

      console.log(`  结果: ${apiResult.latency}ms (误差: ${error}ms) ${isAccurate ? '✅' : '❌'}`);
    } else {
      apiResults.push({
        endpoint,
        latency: null,
        error: null,
        isAccurate: false
      });
      console.log(`  结果: 失败 ❌`);
    }

    // 等待2秒避免请求过快
    await new Promise(resolve => setTimeout(resolve, 2000));
  }

  // 找到最准确的API
  const validResults = apiResults.filter(r => r.latency !== null);
  let bestMatch = null;

  if (validResults.length > 0) {
    bestMatch = validResults.reduce((best, current) =>
      current.error < best.error ? current : best
    );
  }

  // 输出对比结果
  console.log(`\n📊 结果对比:`);
  console.log(`本地ping: ${localPing}ms`);

  apiResults.forEach(result => {
    if (result.latency) {
      const status = result.isAccurate ? '✅' : '❌';
      console.log(`${status} ${result.endpoint}: ${result.latency}ms (误差: ${result.error}ms)`);
    } else {
      console.log(`❌ ${result.endpoint}: 失败`);
    }
  });

  if (bestMatch) {
    console.log(`\n🏆 最佳匹配: ${bestMatch.endpoint} (误差: ${bestMatch.error}ms)`);
  }

  return {
    site: siteUrl,
    localPing,
    apiResults,
    bestMatch
  };
}

async function runTest() {
  console.log(`🚀 开始智能延迟准确性测试`);
  console.log(`目标误差: ±15ms`);
  
  const testSites = [
    'https://cloud.189.cn/',
    'https://wobshare.us.kg/',
    'https://iweec.com/',
    'https://proton.me/',
    'https://www.google.com/'  // 添加Google作为被墙网站测试
  ];
  
  const results = [];
  
  for (const site of testSites) {
    const result = await testSite(site);
    results.push(result);
    
    // 等待2秒
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  // 总结
  console.log(`\n${'='.repeat(60)}`);
  console.log(`📋 测试总结`);
  console.log(`${'='.repeat(60)}`);
  
  const validResults = results.filter(r => r.error !== null);
  const accurateResults = validResults.filter(r => r.isAccurate);
  
  console.log(`\n📊 统计:`);
  console.log(`- 测试网站: ${testSites.length}个`);
  console.log(`- 有效结果: ${validResults.length}个`);
  console.log(`- 准确结果: ${accurateResults.length}个`);
  console.log(`- 准确率: ${validResults.length > 0 ? Math.round(accurateResults.length/validResults.length*100) : 0}%`);
  
  console.log(`\n🎯 详细结果:`);
  results.forEach(r => {
    if (r.error !== null) {
      const status = r.isAccurate ? '✅' : '❌';
      console.log(`${status} ${r.site}: 本地${r.localPing}ms vs API${r.apiPing}ms (误差${r.error}ms)`);
    } else {
      console.log(`❌ ${r.site}: 测试失败`);
    }
  });
  
  console.log(`\n✅ 测试完成！`);
}

runTest().catch(console.error);
