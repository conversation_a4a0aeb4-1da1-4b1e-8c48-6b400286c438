// 🎯 本地ping vs API直接对比测试
const { exec } = require('child_process');
const https = require('https');
const { promisify } = require('util');

const execAsync = promisify(exec);

// 简单的fetch实现
function fetch(url, options = {}) {
  return new Promise((resolve, reject) => {
    const req = https.request(url, {
      method: options.method || 'GET',
      headers: options.headers || {},
      timeout: options.timeout || 30000
    }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          ok: res.statusCode >= 200 && res.statusCode < 300,
          status: res.statusCode,
          json: () => Promise.resolve(JSON.parse(data))
        });
      });
    });
    
    req.on('error', reject);
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    if (options.body) {
      req.write(options.body);
    }
    
    req.end();
  });
}

// 执行本地ping测试
async function localPing(domain) {
  console.log(`\n📍 本地ping测试: ${domain}`);
  console.log(`执行命令: ping -n 4 ${domain}`);
  
  try {
    const { stdout, stderr } = await execAsync(`ping -n 4 ${domain}`, { timeout: 25000 });
    console.log(`本地ping输出:\n${stdout}`);
    
    // 检查是否超时或被墙
    if (stdout.includes('请求超时') || stdout.includes('Request timed out')) {
      console.log(`❌ 本地结果: 请求超时 (被墙)`);
      return { status: 'timeout', latency: null, isBlocked: true };
    }
    
    if (stdout.includes('100% 丢失') || stdout.includes('100% loss')) {
      console.log(`❌ 本地结果: 100%丢包 (被墙)`);
      return { status: 'blocked', latency: null, isBlocked: true };
    }
    
    if (stdout.includes('找不到主机') || stdout.includes('could not find host')) {
      console.log(`❌ 本地结果: 找不到主机`);
      return { status: 'host_not_found', latency: null, isBlocked: true };
    }
    
    // 尝试提取延迟 - 支持中文编码问题
    const avgPatterns = [
      /平均 = (\d+)ms/i,
      /Average = (\d+)ms/i,
      /ƽ�� = (\d+)ms/i,  // 中文编码问题
      /��� = (\d+)ms/i   // 另一种编码
    ];

    for (const pattern of avgPatterns) {
      const match = stdout.match(pattern);
      if (match) {
        const latency = parseInt(match[1]);
        console.log(`✅ 本地结果: ${latency}ms (从统计信息提取)`);
        return { status: 'success', latency: latency, isBlocked: false };
      }
    }

    // 如果没有平均值，尝试提取单次延迟
    const timePatterns = [
      /时间[<=](\d+)ms/gi,
      /time[<=](\d+)ms/gi,
      /ʱ��[<=](\d+)ms/gi,  // 中文编码问题
      /��[<=](\d+)ms/gi     // 另一种编码
    ];

    const latencies = [];
    for (const pattern of timePatterns) {
      const matches = [...stdout.matchAll(pattern)];
      matches.forEach(match => {
        const latency = parseInt(match[1]);
        if (latency && latency > 0) {
          latencies.push(latency);
        }
      });
    }

    if (latencies.length > 0) {
      const avg = Math.round(latencies.reduce((a, b) => a + b, 0) / latencies.length);
      console.log(`✅ 本地结果: ${avg}ms (从${latencies.length}次测量计算: [${latencies.join(', ')}])`);
      return { status: 'success', latency: avg, isBlocked: false };
    }
    
    console.log(`❌ 本地结果: 无法解析延迟数据`);
    return { status: 'parse_failed', latency: null, isBlocked: false };
    
  } catch (error) {
    console.log(`❌ 本地ping执行失败: ${error.message}`);
    return { status: 'error', latency: null, isBlocked: false };
  }
}

// 调用API测试
async function apiTest(targetUrl) {
  console.log(`\n🌐 API测试: ${targetUrl}`);
  console.log(`调用API: https://ping.wobshare.us.kg/api/ping-globalping`);

  try {
    const response = await fetch('https://ping.wobshare.us.kg/api/ping-globalping', {
      method: 'POST',
      headers: { 
        'Content-Type': 'application/json',
        'User-Agent': 'LocalTest/1.0'
      },
      body: JSON.stringify({ target: targetUrl, maxNodes: 10 }),
      timeout: 60000
    });
    
    console.log(`API响应状态: ${response.status}`);
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}`);
    }
    
    const data = await response.json();
    
    if (!data.success || !data.results || data.results.length === 0) {
      console.log(`❌ API结果: 无有效数据`);
      return { status: 'no_data', latency: null, nodeCount: 0 };
    }
    
    // 优先使用中国节点
    const chinaNodes = data.results.filter(r => 
      r.location && (
        r.location.country === 'CN' || 
        r.location.country === 'HK' ||
        r.location.country === 'TW'
      ) && r.ping && r.status === 'success' && r.ping < 999
    );
    
    const validNodes = chinaNodes.length > 0 ? chinaNodes : 
                      data.results.filter(r => r.ping && r.status === 'success' && r.ping < 999);
    
    if (validNodes.length === 0) {
      console.log(`❌ API结果: 无有效节点 (可能被墙)`);
      return { status: 'blocked', latency: null, nodeCount: data.results.length };
    }
    
    const avgLatency = Math.round(validNodes.reduce((sum, r) => sum + r.ping, 0) / validNodes.length);
    console.log(`✅ API结果: ${avgLatency}ms (${validNodes.length}个有效节点，${chinaNodes.length}个中国节点)`);
    
    return { 
      status: 'success', 
      latency: avgLatency, 
      nodeCount: validNodes.length,
      chinaNodes: chinaNodes.length
    };
    
  } catch (error) {
    console.log(`❌ API调用失败: ${error.message}`);
    return { status: 'error', latency: null, nodeCount: 0 };
  }
}

// 对比分析
function compareResults(localResult, apiResult, site) {
  console.log(`\n📊 对比分析: ${site}`);
  console.log(`本地: ${localResult.latency ? localResult.latency + 'ms' : localResult.status}`);
  console.log(`API: ${apiResult.latency ? apiResult.latency + 'ms' : apiResult.status}`);
  
  // 情况1: 本地被墙/超时
  if (localResult.isBlocked) {
    if (apiResult.status === 'blocked' || apiResult.latency === null) {
      console.log(`✅ 一致: 都识别为被墙/无法访问`);
      return { isAccurate: true, error: 0, category: 'blocked_consistent' };
    } else {
      console.log(`❌ 不一致: 本地被墙，API返回${apiResult.latency}ms`);
      return { isAccurate: false, error: null, category: 'blocked_inconsistent' };
    }
  }
  
  // 情况2: 本地失败
  if (localResult.status !== 'success') {
    console.log(`⚠️ 本地测试失败: ${localResult.status}`);
    return { isAccurate: false, error: null, category: 'local_failed' };
  }
  
  // 情况3: API失败
  if (apiResult.status !== 'success') {
    console.log(`❌ API测试失败: ${apiResult.status}`);
    return { isAccurate: false, error: null, category: 'api_failed' };
  }
  
  // 情况4: 都成功，计算误差
  const error = Math.abs(apiResult.latency - localResult.latency);
  const isAccurate = error <= 15;
  
  console.log(`📏 误差: ${error}ms`);
  console.log(`🎯 准确性: ${isAccurate ? '✅ 合格' : '❌ 不合格'} (要求≤15ms)`);
  
  return { isAccurate: isAccurate, error: error, category: 'normal' };
}

// 测试单个网站
async function testSite(siteUrl) {
  console.log(`\n${'='.repeat(80)}`);
  console.log(`🎯 测试网站: ${siteUrl}`);
  console.log(`${'='.repeat(80)}`);
  
  // 提取域名
  let domain;
  try {
    domain = new URL(siteUrl).hostname;
  } catch {
    domain = siteUrl.replace(/^https?:\/\//, '').replace(/\/$/, '');
  }
  
  // 1. 本地ping测试
  const localResult = await localPing(domain);
  
  // 等待2秒
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // 2. API测试
  const apiResult = await apiTest(siteUrl);
  
  // 3. 对比分析
  const comparison = compareResults(localResult, apiResult, siteUrl);
  
  return {
    site: siteUrl,
    domain: domain,
    localResult: localResult,
    apiResult: apiResult,
    comparison: comparison
  };
}

// 主测试函数
async function runTest() {
  console.log(`🚀 本地ping vs API对比测试`);
  console.log(`本地环境: ${process.platform}`);
  console.log(`API地址: https://ping.wobshare.us.kg/`);
  console.log(`要求: ±15ms误差 | 正确识别被墙网站`);
  
  const testSites = [
    'https://cloud.189.cn/',
    'https://wobshare.us.kg/',
    'https://iweec.com/',
    'https://proton.me/',
    'https://www.google.com/'
  ];
  
  const results = [];
  
  for (const site of testSites) {
    const result = await testSite(site);
    results.push(result);
    
    // 等待3秒再测试下一个网站
    await new Promise(resolve => setTimeout(resolve, 3000));
  }
  
  // 生成报告
  console.log(`\n${'='.repeat(100)}`);
  console.log(`📋 测试报告`);
  console.log(`${'='.repeat(100)}`);
  
  const accurateResults = results.filter(r => r.comparison.isAccurate);
  const validTests = results.filter(r => r.comparison.category === 'normal' || r.comparison.category === 'blocked_consistent');
  
  console.log(`\n📊 统计:`);
  console.log(`- 测试网站: ${testSites.length}个`);
  console.log(`- 有效测试: ${validTests.length}个`);
  console.log(`- 准确结果: ${accurateResults.length}个`);
  console.log(`- 准确率: ${validTests.length > 0 ? Math.round(accurateResults.length / validTests.length * 100) : 0}%`);
  
  console.log(`\n🎯 详细结果:`);
  results.forEach(result => {
    const status = result.comparison.isAccurate ? '✅' : '❌';
    const localDesc = result.localResult.latency ? `${result.localResult.latency}ms` : result.localResult.status;
    const apiDesc = result.apiResult.latency ? `${result.apiResult.latency}ms` : result.apiResult.status;
    
    console.log(`${status} ${result.site}:`);
    console.log(`   本地: ${localDesc}`);
    console.log(`   API: ${apiDesc}`);
    
    if (result.comparison.error !== null) {
      console.log(`   误差: ${result.comparison.error}ms`);
    }
  });
  
  console.log(`\n✅ 测试完成！`);
  return results;
}

runTest().catch(console.error);
