// 🎯 准确的Ping API - 直接使用Globalping的真实延迟数据，不进行人为调整
import { NextApiRequest, NextApiResponse } from 'next';

interface GlobalpingRequest {
  type: string;
  target: string;
  limit: number;
  locations: Array<{ country?: string; magic?: string }>;
  measurementOptions: {
    packets: number;
  };
}

interface GlobalpingProbe {
  id: string;
  country: string;
  city: string;
  region: string;
  latitude: number;
  longitude: number;
  asn: number;
  network: string;
}

interface GlobalpingResult {
  probe: GlobalpingProbe;
  result: {
    status: string;
    rawOutput: string;
    stats?: {
      min: number;
      max: number;
      avg: number;
      loss: number;
    };
  };
}

interface GlobalpingResponse {
  id: string;
  type: string;
  status: string;
  createdAt: string;
  updatedAt: string;
  probesCount: number;
  results: GlobalpingResult[];
}

// 🎯 调用Globalping API进行真实ping测试
async function callGlobalpingAPI(target: string, limit: number = 10): Promise<GlobalpingResponse> {
  console.log(`🌐 调用准确Globalping API: ${target}, limit: ${limit}`);
  
  const requestBody: GlobalpingRequest = {
    type: 'ping',
    target: target,
    limit: limit,
    locations: [
      { country: 'CN' }, // 中国节点
      { country: 'HK' }, // 香港节点
      { country: 'TW' }, // 台湾节点
    ],
    measurementOptions: {
      packets: 4
    }
  };

  try {
    // 创建测试
    const response = await fetch('https://api.globalping.io/v1/measurements', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.GLOBALPING_API_TOKEN || 'k5k76pvk6lj7omghnfqlk4naib5kbwcd'}`,
        'User-Agent': 'PingTool/1.0'
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`Failed to create measurement: ${response.status}`);
    }

    const data = await response.json();
    const measurementId = data.id;
    console.log(`📊 测试ID: ${measurementId}`);

    // 等待测试完成
    const maxAttempts = 30;
    let attempts = 0;
    
    while (attempts < maxAttempts) {
      await new Promise(resolve => setTimeout(resolve, 2000)); // 等待2秒
      
      const resultResponse = await fetch(`https://api.globalping.io/v1/measurements/${measurementId}`, {
        headers: {
          'Authorization': `Bearer ${process.env.GLOBALPING_API_TOKEN || 'k5k76pvk6lj7omghnfqlk4naib5kbwcd'}`,
          'User-Agent': 'PingTool/1.0'
        }
      });

      if (!resultResponse.ok) {
        throw new Error(`Failed to get results: ${resultResponse.status}`);
      }

      const result: GlobalpingResponse = await resultResponse.json();
      
      if (result.status === 'finished') {
        console.log(`✅ 测试完成，获得 ${result.results.length} 个结果`);
        return result;
      }
      
      attempts++;
      console.log(`⏳ 等待测试完成... (${attempts}/${maxAttempts})`);
    }
    
    throw new Error('测试超时');
  } catch (error) {
    console.error('❌ Globalping API调用失败:', error);
    throw error;
  }
}

// 🔍 从rawOutput中提取真实的ping延迟
function extractRealLatencyFromRawOutput(rawOutput: string): number | null {
  if (!rawOutput) return null;
  
  // 匹配ping时间的多种格式
  const patterns = [
    /time[=<](\d+(?:\.\d+)?)\s*ms/gi,
    /(\d+(?:\.\d+)?)\s*ms/gi
  ];
  
  const latencies: number[] = [];
  
  for (const pattern of patterns) {
    const matches = [...rawOutput.matchAll(pattern)];
    for (const match of matches) {
      const latency = parseFloat(match[1]);
      if (latency && latency > 0 && latency < 10000) { // 合理的延迟范围
        latencies.push(latency);
      }
    }
    
    if (latencies.length > 0) break; // 如果找到了延迟数据，就不用尝试其他模式
  }
  
  if (latencies.length === 0) return null;
  
  // 计算平均延迟
  const avgLatency = latencies.reduce((sum, lat) => sum + lat, 0) / latencies.length;
  return Math.round(avgLatency);
}

// 🔄 处理Globalping结果 - 使用真实延迟数据
function processAccurateGlobalpingResults(results: GlobalpingResult[], target: string): any[] {
  return results.map((result, index) => {
    const probe = result.probe;
    const pingResult = result.result;
    
    let status = 'success';
    let ping = 0;
    
    if (pingResult.status === 'failed') {
      status = 'error';
      ping = 999;
    } else {
      // 🎯 关键改进：优先使用rawOutput中的真实延迟
      const realLatency = extractRealLatencyFromRawOutput(pingResult.rawOutput);
      
      if (realLatency !== null) {
        ping = realLatency;
        console.log(`📊 ${probe.city}: 从rawOutput提取真实延迟 ${ping}ms`);
      } else if (pingResult.stats) {
        // 如果无法从rawOutput提取，才使用stats
        const loss = pingResult.stats.loss || 0;
        const avg = pingResult.stats.avg;
        
        if (loss >= 100) {
          status = 'timeout';
          ping = 999;
        } else if (loss >= 50) {
          status = 'blocked';
          ping = avg || 999;
        } else {
          ping = Math.round(avg || 0);
        }
        console.log(`📊 ${probe.city}: 使用stats延迟 ${ping}ms (丢包率: ${loss}%)`);
      } else {
        status = 'error';
        ping = 999;
      }
    }

    return {
      node: `${probe.city || probe.country}-Accurate`,
      province: probe.city || probe.country,
      ping: ping,
      status: status,
      timestamp: Date.now(),
      location: {
        city: probe.city || probe.country,
        country: probe.country,
        region: probe.region,
        province: probe.city || probe.country,
        latitude: probe.latitude,
        longitude: probe.longitude,
        asn: probe.asn,
        network: probe.network
      },
      apiSource: 'Globalping-Accurate',
      testMethod: 'Globalping真实测试(无调整)',
      priority: 1,
      confidence: 0.98, // 更高的置信度，因为使用真实数据
      networkTier: probe.country === 'CN' ? 1 : 2,
      networkQuality: ping < 50 ? 'excellent' : ping < 100 ? 'good' : ping < 200 ? 'average' : 'poor',
      rawOutput: pingResult.rawOutput,
      packetLoss: pingResult.stats?.loss || 0,
      packetsTotal: 4,
      packetsReceived: pingResult.stats ? Math.round(4 * (100 - (pingResult.stats.loss || 0)) / 100) : 0,
      realLatencyExtracted: realLatency !== null
    };
  });
}

// 🔄 转换结果格式
function convertAccurateGlobalpingResults(globalpingResponse: GlobalpingResponse, target: string) {
  const results = processAccurateGlobalpingResults(globalpingResponse.results, target);
  
  // 计算平均延迟（只计算成功的结果）
  const successResults = results.filter(r => r.status === 'success' && r.ping < 999);
  const averageLatency = successResults.length > 0 
    ? Math.round(successResults.reduce((sum, r) => sum + r.ping, 0) / successResults.length)
    : 0;

  return {
    success: true,
    results,
    metadata: {
      totalNodes: results.length,
      successfulNodes: successResults.length,
      averageLatency,
      dataSource: 'Globalping Real Network Tests (Accurate)',
      testMethod: 'globalping-accurate-api',
      target,
      measurementId: globalpingResponse.id,
      fastMode: false,
      realLatencyUsed: results.filter(r => r.realLatencyExtracted).length
    },
    features: {
      monitoringEnabled: true,
      historicalDataAvailable: true,
      recommendationsGenerated: false,
      accurateLatency: true
    },
    timestamp: new Date().toISOString()
  };
}

const handler = async function(req: NextApiRequest, res: NextApiResponse) {
  // 设置CORS头
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { target, maxNodes = 20 } = req.body;

    if (!target) {
      return res.status(400).json({ error: 'Target is required' });
    }

    console.log(`🎯 准确Ping测试请求: ${target}`);

    // 调用Globalping API
    const globalpingResponse = await callGlobalpingAPI(target, Math.min(maxNodes, 50));
    
    // 转换结果格式
    const response = convertAccurateGlobalpingResults(globalpingResponse, target);

    console.log(`✅ 返回 ${response.results.length} 个准确测试结果`);
    console.log(`📊 真实延迟提取成功: ${response.metadata.realLatencyUsed}/${response.results.length}`);

    res.status(200).json(response);
  } catch (error) {
    console.error('❌ 准确Ping测试失败:', error);
    res.status(500).json({ 
      error: 'Accurate ping test failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

export default handler;
