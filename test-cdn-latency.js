// 🎯 测试CDN延迟修复
const https = require('https');

function fetch(url, options = {}) {
  return new Promise((resolve, reject) => {
    const req = https.request(url, {
      method: options.method || 'GET',
      headers: options.headers || {},
      timeout: options.timeout || 30000
    }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          ok: res.statusCode >= 200 && res.statusCode < 300,
          status: res.statusCode,
          json: () => Promise.resolve(JSON.parse(data))
        });
      });
    });
    
    req.on('error', reject);
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    if (options.body) {
      req.write(options.body);
    }
    
    req.end();
  });
}

async function testCDNLatency() {
  console.log('🎯 测试CDN延迟修复');
  
  const target = 'wobshare.us.kg';
  
  try {
    console.log(`\n测试目标: ${target}`);
    
    // 测试Cloudflare Workers API
    console.log('\n🔥 测试Cloudflare Workers API:');
    const cfResponse = await fetch('https://ping.wobshare.us.kg/api/ping-cloudflare-worker', {
      method: 'POST',
      headers: { 
        'Content-Type': 'application/json',
        'User-Agent': 'CDNLatencyTest/1.0'
      },
      body: JSON.stringify({ target }),
      timeout: 15000
    });
    
    if (cfResponse.ok) {
      const cfData = await cfResponse.json();
      if (cfData.success && cfData.results && cfData.results.length > 0) {
        console.log(`✅ Cloudflare Workers成功:`);
        cfData.results.forEach((result, index) => {
          console.log(`  节点${index + 1}: ${result.node || result.city || 'Unknown'} - ${result.ping}ms (${result.testMethod || 'Unknown'})`);
        });
        
        // 分析延迟分布
        const latencies = cfData.results.map(r => r.ping).filter(p => p && p < 999);
        if (latencies.length > 0) {
          const avgLatency = Math.round(latencies.reduce((a, b) => a + b, 0) / latencies.length);
          const minLatency = Math.min(...latencies);
          const maxLatency = Math.max(...latencies);
          
          console.log(`📊 延迟统计: 平均${avgLatency}ms, 最小${minLatency}ms, 最大${maxLatency}ms`);
          
          // 检查是否符合预期
          if (avgLatency < 300 && minLatency > 30 && maxLatency < 500) {
            console.log(`✅ 延迟范围合理！`);
          } else {
            console.log(`❌ 延迟范围异常: 期望30-500ms，实际${minLatency}-${maxLatency}ms`);
          }
        }
      } else {
        console.log(`❌ Cloudflare Workers返回无效数据`);
      }
    } else {
      console.log(`❌ Cloudflare Workers API调用失败: HTTP ${cfResponse.status}`);
    }
    
    // 等待2秒
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 测试Enhanced Ping API（用于对比）
    console.log('\n🎯 测试Enhanced Ping API（对比）:');
    const enhancedResponse = await fetch('https://ping.wobshare.us.kg/api/enhanced-ping', {
      method: 'POST',
      headers: { 
        'Content-Type': 'application/json',
        'User-Agent': 'CDNLatencyTest/1.0'
      },
      body: JSON.stringify({ target }),
      timeout: 15000
    });
    
    if (enhancedResponse.ok) {
      const enhancedData = await enhancedResponse.json();
      if (enhancedData.success && enhancedData.metadata) {
        console.log(`✅ Enhanced Ping成功: 平均延迟${enhancedData.metadata.averageLatency}ms`);
        console.log(`📊 数据源: ${enhancedData.metadata.dataSource}`);
        console.log(`🔧 测试方法: ${enhancedData.metadata.testMethod}`);
      }
    } else {
      console.log(`❌ Enhanced Ping API调用失败: HTTP ${enhancedResponse.status}`);
    }
    
  } catch (error) {
    console.log(`❌ 测试异常: ${error.message}`);
  }
  
  console.log('\n✅ CDN延迟测试完成！');
  console.log('\n💡 预期结果:');
  console.log('- 中国节点: 60-100ms');
  console.log('- 亚太节点: 60-150ms');
  console.log('- 欧洲节点: 185-200ms');
  console.log('- 北美节点: 215-240ms');
  console.log('- 南美节点: 270-330ms');
  console.log('- 非洲节点: 320-380ms');
}

testCDNLatency().catch(console.error);
