// 🎯 测试超精确API
const https = require('https');

function fetch(url, options = {}) {
  return new Promise((resolve, reject) => {
    const req = https.request(url, {
      method: options.method || 'GET',
      headers: options.headers || {},
      timeout: options.timeout || 30000
    }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          ok: res.statusCode >= 200 && res.statusCode < 300,
          status: res.statusCode,
          json: () => Promise.resolve(JSON.parse(data))
        });
      });
    });
    
    req.on('error', reject);
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    if (options.body) {
      req.write(options.body);
    }
    
    req.end();
  });
}

async function testUltraPreciseAPI() {
  console.log('🎯 测试超精确API');
  
  const testSites = [
    'naiyous.com',
    'bulianglin.com',
    'pixiv.net',
    'grok.com',
    'greasyfork.org'
  ];
  
  for (const site of testSites) {
    console.log(`\n测试网站: ${site}`);
    
    try {
      const response = await fetch('https://ping.wobshare.us.kg/api/ultra-precise-ping', {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/json',
          'User-Agent': 'UltraPreciseTest/1.0'
        },
        body: JSON.stringify({ target: site }),
        timeout: 60000
      });
      
      console.log(`API响应状态: ${response.status}`);
      
      if (!response.ok) {
        console.log(`❌ API调用失败: HTTP ${response.status}`);
        continue;
      }
      
      const data = await response.json();
      
      if (data.success && data.results && data.results.length > 0) {
        const avgLatency = data.metadata?.averageLatency || 0;
        const dataSource = data.metadata?.dataSource || '未知';
        const testMethod = data.results[0]?.testMethod || '未知';
        
        console.log(`✅ API成功: 平均延迟${avgLatency}ms`);
        console.log(`📊 数据源: ${dataSource}`);
        console.log(`🔧 测试方法: ${testMethod}`);
        
        // 显示前5个节点的延迟
        const topNodes = data.results.slice(0, 5);
        console.log('前5个节点:');
        topNodes.forEach(node => {
          console.log(`  ${node.node}: ${node.ping}ms (${node.testMethod})`);
        });
      } else {
        console.log(`❌ API返回无效数据`);
      }
      
    } catch (error) {
      console.log(`❌ API调用异常: ${error.message}`);
    }
    
    // 等待2秒再测试下一个
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  console.log('\n✅ 超精确API测试完成！');
}

testUltraPreciseAPI().catch(console.error);
