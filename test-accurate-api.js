// 🎯 测试准确API的脚本
const { exec } = require('child_process');
const https = require('https');
const { promisify } = require('util');

const execAsync = promisify(exec);

// 简单的fetch实现
function fetch(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    
    const req = https.request(url, {
      method: options.method || 'GET',
      headers: options.headers || {},
      timeout: options.timeout || 30000
    }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          ok: res.statusCode >= 200 && res.statusCode < 300,
          status: res.statusCode,
          json: () => Promise.resolve(JSON.parse(data))
        });
      });
    });
    
    req.on('error', reject);
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    if (options.body) {
      req.write(options.body);
    }
    
    req.end();
  });
}

// 本地ping测试
async function testLocalPing(domain) {
  console.log(`📍 测试本地ping: ${domain}`);
  
  try {
    const { stdout } = await execAsync(`ping -n 4 ${domain}`, { timeout: 20000 });
    console.log('Ping输出:', stdout);
    
    // 检查是否包含超时或无法访问的信息
    if (stdout.includes('请求超时') || stdout.includes('Request timed out') || 
        stdout.includes('无法访问') || stdout.includes('Destination host unreachable') ||
        stdout.includes('100% 丢失') || stdout.includes('100% loss') ||
        stdout.includes('找不到主机') || stdout.includes('could not find host')) {
      console.log(`❌ 网站无法访问或被墙: ${domain}`);
      return { latency: null, isBlocked: true, status: 'blocked' };
    }
    
    // 首先尝试直接从统计信息中提取平均值
    const avgMatch = stdout.match(/ƽ�� = (\d+)ms/i) || stdout.match(/平均 = (\d+)ms/i) || stdout.match(/Average = (\d+)ms/i);
    if (avgMatch) {
      const avg = parseInt(avgMatch[1]);
      if (avg && avg > 0) {
        console.log(`✅ 从统计信息提取平均值: ${avg}ms`);
        return { latency: avg, isBlocked: false, status: 'success' };
      }
    }
    
    // 如果没有统计信息，从每次ping中提取
    const patterns = [
      /时间[<=](\d+)ms/gi,
      /time[<=](\d+)ms/gi,
      /ʱ��=(\d+)ms/gi,  // 中文编码问题
    ];
    
    const results = [];
    for (const pattern of patterns) {
      const matches = [...stdout.matchAll(pattern)];
      matches.forEach(match => {
        const latency = parseInt(match[1]);
        if (latency && latency > 0) {
          results.push(latency);
        }
      });
    }
    
    if (results.length > 0) {
      const avg = Math.round(results.reduce((a, b) => a + b, 0) / results.length);
      console.log(`✅ 本地ping结果: ${avg}ms (${results.length}次测量)`);
      return { latency: avg, isBlocked: false, status: 'success' };
    } else {
      console.log(`❌ 无法解析ping结果`);
      return { latency: null, isBlocked: false, status: 'failed' };
    }
    
  } catch (error) {
    console.log(`❌ 本地ping失败: ${error.message}`);
    if (error.message.includes('timeout') || error.message.includes('超时')) {
      return { latency: null, isBlocked: true, status: 'timeout' };
    }
    return { latency: null, isBlocked: false, status: 'error' };
  }
}

// 测试准确API
async function testAccurateAPI(targetUrl) {
  console.log(`🌐 测试准确API: ${targetUrl}`);
  
  try {
    const apiUrl = `https://ping.wobshare.us.kg/api/ping-globalping`;
    console.log(`调用API: ${apiUrl}`);
    
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: { 
        'Content-Type': 'application/json',
        'User-Agent': 'PingTest/1.0'
      },
      body: JSON.stringify({ target: targetUrl, maxNodes: 10 }),
      timeout: 60000
    });
    
    console.log(`API响应状态: ${response.status}`);
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }
    
    const data = await response.json();
    console.log('API响应数据:', JSON.stringify(data, null, 2));
    
    if (data.results && data.results.length > 0) {
      // 优先使用中国节点
      const chinaNodes = data.results.filter(r => 
        r.location && (
          r.location.country === 'CN' || 
          r.location.country === 'China' ||
          r.location.country === 'HK' ||
          r.location.country === 'TW'
        ) && r.ping && r.status === 'success'
      );
      
      const validNodes = chinaNodes.length > 0 ? chinaNodes : 
                        data.results.filter(r => r.ping && r.status === 'success');
      
      if (validNodes.length > 0) {
        const avg = Math.round(validNodes.reduce((sum, r) => sum + r.ping, 0) / validNodes.length);
        const realLatencyCount = validNodes.filter(r => r.realLatencyExtracted).length;
        
        console.log(`✅ API延迟: ${avg}ms (${validNodes.length}个有效节点，${chinaNodes.length}个中国节点)`);
        console.log(`📊 真实延迟提取: ${realLatencyCount}/${validNodes.length}个节点`);
        
        return { 
          latency: avg, 
          nodeCount: validNodes.length, 
          chinaNodes: chinaNodes.length,
          realLatencyCount: realLatencyCount,
          isBlocked: avg > 500
        };
      }
    }
    
    console.log(`❌ 无法从API响应中提取延迟数据`);
    return null;
    
  } catch (error) {
    console.log(`❌ API调用失败: ${error.message}`);
    return null;
  }
}

// 对比测试
async function compareTest(siteUrl) {
  console.log(`\n${'='.repeat(60)}`);
  console.log(`🎯 对比测试: ${siteUrl}`);
  console.log(`${'='.repeat(60)}`);
  
  // 提取域名
  let domain;
  try {
    domain = new URL(siteUrl).hostname;
  } catch {
    domain = siteUrl.replace(/^https?:\/\//, '').replace(/\/$/, '');
  }
  
  console.log(`域名: ${domain}`);
  
  // 本地ping测试
  const localResult = await testLocalPing(domain);
  
  if (localResult.isBlocked) {
    console.log(`⚠️ 本地ping显示网站被墙`);
    return { site: siteUrl, localResult, apiResult: null, error: null, isAccurate: false };
  } else if (!localResult.latency) {
    console.log(`⚠️ 本地ping失败`);
    return { site: siteUrl, localResult, apiResult: null, error: null, isAccurate: false };
  }
  
  // 等待2秒
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // API测试
  const apiResult = await testAccurateAPI(siteUrl);
  
  if (!apiResult) {
    console.log(`❌ API测试失败`);
    return { site: siteUrl, localResult, apiResult: null, error: null, isAccurate: false };
  }
  
  // 计算误差
  const error = Math.abs(apiResult.latency - localResult.latency);
  const isAccurate = error <= 15;
  
  console.log(`\n📊 结果对比:`);
  console.log(`本地ping: ${localResult.latency}ms`);
  console.log(`API ping: ${apiResult.latency}ms`);
  console.log(`误差: ${error}ms`);
  console.log(`准确性: ${isAccurate ? '✅ 合格' : '❌ 不合格'} (要求≤15ms)`);
  console.log(`真实延迟提取: ${apiResult.realLatencyCount}/${apiResult.nodeCount}个节点`);
  
  return { 
    site: siteUrl, 
    localResult, 
    apiResult, 
    error, 
    isAccurate 
  };
}

// 主测试函数
async function runAccurateTest() {
  console.log(`🚀 开始准确API测试`);
  console.log(`目标误差: ±15ms`);
  
  const testSites = [
    'https://cloud.189.cn/',
    'https://wobshare.us.kg/',
    'https://iweec.com/',
    'https://proton.me/',
    'https://www.google.com/'
  ];
  
  const results = [];
  
  for (const site of testSites) {
    const result = await compareTest(site);
    results.push(result);
    
    // 等待3秒再测试下一个网站
    await new Promise(resolve => setTimeout(resolve, 3000));
  }
  
  // 生成总结报告
  console.log(`\n${'='.repeat(80)}`);
  console.log(`📋 准确API测试总结`);
  console.log(`${'='.repeat(80)}`);
  
  const validResults = results.filter(r => r.error !== null);
  const accurateResults = validResults.filter(r => r.isAccurate);
  const blockedSites = results.filter(r => r.localResult.isBlocked);
  
  console.log(`\n📊 统计:`);
  console.log(`- 测试网站: ${testSites.length}个`);
  console.log(`- 有效结果: ${validResults.length}个`);
  console.log(`- 准确结果: ${accurateResults.length}个`);
  console.log(`- 被墙网站: ${blockedSites.length}个`);
  console.log(`- 准确率: ${validResults.length > 0 ? Math.round(accurateResults.length/validResults.length*100) : 0}%`);
  
  console.log(`\n🎯 详细结果:`);
  results.forEach(r => {
    if (r.localResult.isBlocked) {
      console.log(`🚫 ${r.site}: 被墙网站`);
    } else if (r.error !== null) {
      const status = r.isAccurate ? '✅' : '❌';
      console.log(`${status} ${r.site}: 本地${r.localResult.latency}ms vs API${r.apiResult.latency}ms (误差${r.error}ms)`);
      if (r.apiResult.realLatencyCount) {
        console.log(`   📊 真实延迟提取: ${r.apiResult.realLatencyCount}/${r.apiResult.nodeCount}个节点`);
      }
    } else {
      console.log(`❌ ${r.site}: 测试失败`);
    }
  });
  
  // 最终评估
  console.log(`\n💡 最终评估:`);
  const overallAccuracy = validResults.length > 0 ? Math.round(accurateResults.length / validResults.length * 100) : 0;
  
  if (overallAccuracy >= 80) {
    console.log(`✅ 准确API性能优秀，满足±15ms准确性要求！`);
  } else if (overallAccuracy >= 60) {
    console.log(`⚠️ 准确API性能良好，但仍有改进空间`);
  } else {
    console.log(`❌ 准确API性能需要进一步优化`);
  }
  
  // 分析真实延迟提取情况
  const totalNodes = validResults.reduce((sum, r) => sum + (r.apiResult?.nodeCount || 0), 0);
  const realLatencyNodes = validResults.reduce((sum, r) => sum + (r.apiResult?.realLatencyCount || 0), 0);
  const realLatencyRate = totalNodes > 0 ? Math.round(realLatencyNodes / totalNodes * 100) : 0;
  
  console.log(`📊 真实延迟提取率: ${realLatencyRate}% (${realLatencyNodes}/${totalNodes}个节点)`);
  
  if (realLatencyRate >= 80) {
    console.log(`✅ 真实延迟提取率很高，数据质量优秀！`);
  } else {
    console.log(`⚠️ 真实延迟提取率较低，可能影响准确性`);
  }
  
  console.log(`\n✅ 测试完成！`);
  return results;
}

runAccurateTest().catch(console.error);
