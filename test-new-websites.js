// 🎯 测试新网站列表的本地ping vs API对比
const { exec } = require('child_process');
const https = require('https');
const { promisify } = require('util');

const execAsync = promisify(exec);

// 简单的fetch实现
function fetch(url, options = {}) {
  return new Promise((resolve, reject) => {
    const req = https.request(url, {
      method: options.method || 'GET',
      headers: options.headers || {},
      timeout: options.timeout || 30000
    }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          ok: res.statusCode >= 200 && res.statusCode < 300,
          status: res.statusCode,
          json: () => Promise.resolve(JSON.parse(data))
        });
      });
    });
    
    req.on('error', reject);
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    if (options.body) {
      req.write(options.body);
    }
    
    req.end();
  });
}

// 本地ping测试
async function performLocalPing(domain) {
  console.log(`📍 本地ping测试: ${domain}`);
  
  try {
    const { stdout, stderr } = await execAsync(`ping -n 4 ${domain}`, { timeout: 25000 });
    console.log(`本地ping输出:\n${stdout}`);
    
    // 检查各种失败情况
    if (stdout.includes('请求超时') || stdout.includes('Request timed out')) {
      console.log(`❌ 本地ping: 请求超时 (被墙网站)`);
      return { status: 'timeout', latency: null, isBlocked: true, reason: '请求超时' };
    }
    
    if (stdout.includes('无法访问目标主机') || stdout.includes('Destination host unreachable')) {
      console.log(`❌ 本地ping: 无法访问目标主机`);
      return { status: 'unreachable', latency: null, isBlocked: true, reason: '无法访问目标主机' };
    }
    
    if (stdout.includes('找不到主机') || stdout.includes('could not find host') || stdout.includes('Ping 请求找不到主机')) {
      console.log(`❌ 本地ping: 找不到主机`);
      return { status: 'host_not_found', latency: null, isBlocked: true, reason: '找不到主机' };
    }
    
    if (stdout.includes('100% 丢失') || stdout.includes('100% loss')) {
      console.log(`❌ 本地ping: 100%丢包`);
      return { status: 'packet_loss', latency: null, isBlocked: true, reason: '100%丢包' };
    }
    
    // 尝试提取延迟数据
    // 首先尝试从统计信息提取平均值
    const avgPatterns = [
      /平均 = (\d+)ms/i,
      /Average = (\d+)ms/i,
      /ƽ�� = (\d+)ms/i
    ];
    
    for (const pattern of avgPatterns) {
      const match = stdout.match(pattern);
      if (match) {
        const avg = parseInt(match[1]);
        if (avg && avg > 0) {
          console.log(`✅ 本地ping: ${avg}ms (从统计信息提取)`);
          return { status: 'success', latency: avg, isBlocked: false, reason: '统计平均值' };
        }
      }
    }
    
    // 如果没有统计信息，从每次ping中提取
    const timePatterns = [
      /时间[<=](\d+)ms/gi,
      /time[<=](\d+)ms/gi,
      /ʱ��[<=](\d+)ms/gi
    ];
    
    const latencies = [];
    for (const pattern of timePatterns) {
      const matches = [...stdout.matchAll(pattern)];
      matches.forEach(match => {
        const latency = parseInt(match[1]);
        if (latency && latency > 0) {
          latencies.push(latency);
        }
      });
    }
    
    if (latencies.length > 0) {
      const avg = Math.round(latencies.reduce((a, b) => a + b, 0) / latencies.length);
      console.log(`✅ 本地ping: ${avg}ms (从${latencies.length}次测量计算平均值: [${latencies.join(', ')}])`);
      return { status: 'success', latency: avg, isBlocked: false, reason: `${latencies.length}次测量平均` };
    }
    
    console.log(`❌ 本地ping: 无法解析延迟数据`);
    return { status: 'parse_failed', latency: null, isBlocked: false, reason: '无法解析延迟' };
    
  } catch (error) {
    console.log(`❌ 本地ping执行失败: ${error.message}`);
    if (error.message.includes('timeout') || error.message.includes('超时')) {
      return { status: 'command_timeout', latency: null, isBlocked: true, reason: '命令执行超时' };
    }
    return { status: 'command_error', latency: null, isBlocked: false, reason: `执行错误: ${error.message}` };
  }
}

// API测试
async function performAPITest(targetUrl) {
  console.log(`🌐 API测试: ${targetUrl}`);
  
  try {
    const response = await fetch('https://ping.wobshare.us.kg/api/enhanced-ping', {
      method: 'POST',
      headers: { 
        'Content-Type': 'application/json',
        'User-Agent': 'LocalTest/1.0'
      },
      body: JSON.stringify({ target: targetUrl }),
      timeout: 60000
    });
    
    console.log(`API响应状态: ${response.status}`);
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}`);
    }
    
    const data = await response.json();
    
    if (!data.success || !data.results || data.results.length === 0) {
      console.log(`❌ API结果: 无有效数据`);
      return { status: 'no_data', latency: null, nodeCount: 0 };
    }
    
    // 计算平均延迟
    const validResults = data.results.filter(r => r.ping && r.ping < 999);
    
    if (validResults.length === 0) {
      console.log(`❌ API结果: 无有效节点 (可能被墙)`);
      return { status: 'blocked', latency: null, nodeCount: data.results.length };
    }
    
    const avgLatency = Math.round(validResults.reduce((sum, r) => sum + r.ping, 0) / validResults.length);
    const blockedCount = data.results.filter(r => r.ping >= 999).length;
    
    console.log(`✅ API结果: ${avgLatency}ms (${validResults.length}个有效节点，${blockedCount}个被墙节点)`);
    console.log(`📊 数据源: ${data.metadata?.dataSource || '未知'}`);
    console.log(`🔧 测试方法: ${data.metadata?.testMethod || '未知'}`);
    
    return { 
      status: 'success', 
      latency: avgLatency, 
      nodeCount: validResults.length,
      blockedCount: blockedCount,
      dataSource: data.metadata?.dataSource,
      testMethod: data.metadata?.testMethod
    };
    
  } catch (error) {
    console.log(`❌ API调用失败: ${error.message}`);
    return { status: 'error', latency: null, nodeCount: 0 };
  }
}

// 对比分析
function analyzeComparison(localResult, apiResult, siteUrl) {
  console.log(`\n📊 对比分析: ${siteUrl}`);
  console.log(`本地结果: ${localResult.status} - ${localResult.latency ? localResult.latency + 'ms' : '无延迟'} (${localResult.reason})`);
  console.log(`API结果: ${apiResult.status} - ${apiResult.latency ? apiResult.latency + 'ms' : '无延迟'}`);
  
  // 情况1: 本地被墙/超时
  if (localResult.isBlocked) {
    if (apiResult.status === 'blocked' || apiResult.latency === null || apiResult.latency > 500) {
      console.log(`✅ 一致性: 都识别为被墙/无法访问`);
      return { 
        isConsistent: true, 
        error: 0, 
        isAccurate: true, 
        category: 'blocked_consistent',
        message: '都正确识别为被墙网站'
      };
    } else {
      console.log(`❌ 不一致: 本地被墙，但API返回${apiResult.latency}ms`);
      return { 
        isConsistent: false, 
        error: null, 
        isAccurate: false, 
        category: 'blocked_inconsistent',
        message: `本地被墙，API却返回${apiResult.latency}ms`
      };
    }
  }
  
  // 情况2: 本地失败
  if (localResult.status !== 'success') {
    console.log(`⚠️ 本地测试失败，无法对比: ${localResult.reason}`);
    return { 
      isConsistent: false, 
      error: null, 
      isAccurate: false, 
      category: 'local_failed',
      message: `本地测试失败: ${localResult.reason}`
    };
  }
  
  // 情况3: API失败
  if (apiResult.status !== 'success') {
    console.log(`❌ API测试失败，无法对比`);
    return { 
      isConsistent: false, 
      error: null, 
      isAccurate: false, 
      category: 'api_failed',
      message: `API测试失败`
    };
  }
  
  // 情况4: 都成功，计算误差
  const error = Math.abs(apiResult.latency - localResult.latency);
  const isAccurate = error <= 15;
  
  console.log(`📏 延迟误差: ${error}ms`);
  console.log(`🎯 准确性: ${isAccurate ? '✅ 合格' : '❌ 不合格'} (要求≤15ms)`);
  
  return { 
    isConsistent: true, 
    error: error, 
    isAccurate: isAccurate, 
    category: 'normal_comparison',
    message: `误差${error}ms ${isAccurate ? '(合格)' : '(超出±15ms要求)'}`
  };
}

// 测试单个网站
async function testSingleSite(siteUrl) {
  console.log(`\n${'='.repeat(80)}`);
  console.log(`🎯 测试网站: ${siteUrl}`);
  console.log(`${'='.repeat(80)}`);
  
  // 提取域名
  let domain;
  try {
    domain = new URL(siteUrl).hostname;
  } catch {
    domain = siteUrl.replace(/^https?:\/\//, '').replace(/\/$/, '');
  }
  
  console.log(`域名: ${domain}`);
  
  // 1. 本地ping测试
  const localResult = await performLocalPing(domain);
  
  // 等待2秒
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // 2. API测试
  const apiResult = await performAPITest(siteUrl);
  
  // 3. 对比分析
  const comparison = analyzeComparison(localResult, apiResult, siteUrl);
  
  return {
    site: siteUrl,
    domain: domain,
    localResult: localResult,
    apiResult: apiResult,
    comparison: comparison
  };
}

// 主测试函数
async function runNewWebsiteTest() {
  console.log(`🚀 新网站列表测试`);
  console.log(`API地址: https://ping.wobshare.us.kg/`);
  console.log(`要求: ±15ms误差 | 正确识别被墙网站`);
  
  const testSites = [
    'https://www.pixiv.net/',
    'https://grok.com/',
    'https://greasyfork.org/',
    'https://naiyous.com/',
    'https://bulianglin.com/'
  ];
  
  const results = [];
  
  for (const site of testSites) {
    const result = await testSingleSite(site);
    results.push(result);
    
    // 等待3秒再测试下一个网站
    await new Promise(resolve => setTimeout(resolve, 3000));
  }
  
  // 生成报告
  console.log(`\n${'='.repeat(100)}`);
  console.log(`📋 新网站测试报告`);
  console.log(`${'='.repeat(100)}`);
  
  const accurateResults = results.filter(r => r.comparison.isAccurate);
  const validTests = results.filter(r => r.comparison.category === 'normal_comparison' || r.comparison.category === 'blocked_consistent');
  
  console.log(`\n📊 统计:`);
  console.log(`- 测试网站: ${testSites.length}个`);
  console.log(`- 有效测试: ${validTests.length}个`);
  console.log(`- 准确结果: ${accurateResults.length}个`);
  console.log(`- 准确率: ${validTests.length > 0 ? Math.round(accurateResults.length / validTests.length * 100) : 0}%`);
  
  console.log(`\n🎯 详细结果:`);
  results.forEach(result => {
    const status = result.comparison.isAccurate ? '✅' : '❌';
    const localDesc = result.localResult.latency ? `${result.localResult.latency}ms` : result.localResult.status;
    const apiDesc = result.apiResult.latency ? `${result.apiResult.latency}ms` : result.apiResult.status;
    
    console.log(`${status} ${result.site}:`);
    console.log(`   本地: ${localDesc} (${result.localResult.reason || result.localResult.status})`);
    console.log(`   API: ${apiDesc} (${result.apiResult.dataSource || 'Unknown'})`);
    console.log(`   结果: ${result.comparison.message}`);
    
    if (result.comparison.error !== null) {
      console.log(`   误差: ${result.comparison.error}ms`);
    }
  });
  
  // 最终评估
  console.log(`\n💡 最终评估:`);
  const overallAccuracy = validTests.length > 0 ? Math.round(accurateResults.length / validTests.length * 100) : 0;
  
  if (overallAccuracy >= 80) {
    console.log(`✅ API性能优秀，满足±15ms准确性要求！`);
  } else if (overallAccuracy >= 60) {
    console.log(`⚠️ API性能良好，但仍有改进空间`);
  } else {
    console.log(`❌ API性能需要进一步优化`);
  }
  
  console.log(`\n✅ 新网站测试完成！`);
  return results;
}

runNewWebsiteTest().catch(console.error);
