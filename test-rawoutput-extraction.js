// 测试rawOutput延迟提取功能
function extractRealLatencyFromRawOutput(rawOutput) {
  if (!rawOutput) return null;
  
  console.log('原始rawOutput:', rawOutput);
  
  // 匹配ping时间的多种格式
  const patterns = [
    /time[=<](\d+(?:\.\d+)?)\s*ms/gi,
    /(\d+(?:\.\d+)?)\s*ms/gi
  ];
  
  const latencies = [];
  
  for (const pattern of patterns) {
    console.log(`尝试模式: ${pattern}`);
    const matches = [...rawOutput.matchAll(pattern)];
    console.log(`找到匹配: ${matches.length}个`);
    
    for (const match of matches) {
      console.log(`匹配内容: ${match[0]}, 提取值: ${match[1]}`);
      const latency = parseFloat(match[1]);
      if (latency && latency > 0 && latency < 10000) { // 合理的延迟范围
        latencies.push(latency);
        console.log(`添加延迟: ${latency}ms`);
      }
    }
    
    if (latencies.length > 0) {
      console.log(`使用模式 ${pattern} 找到 ${latencies.length} 个延迟值`);
      break; // 如果找到了延迟数据，就不用尝试其他模式
    }
  }
  
  console.log(`所有延迟值: [${latencies.join(', ')}]`);
  
  if (latencies.length === 0) return null;
  
  // 计算平均延迟
  const avgLatency = latencies.reduce((sum, lat) => sum + lat, 0) / latencies.length;
  console.log(`平均延迟: ${avgLatency}ms`);
  return Math.round(avgLatency);
}

// 测试用例
const testCases = [
  {
    name: '北京节点 - cloud.189.cn',
    rawOutput: `PING cloud189.21cn.com (14.116.220.47) 56(84) bytes of data.
64 bytes from 14.116.220.47 (14.116.220.47): icmp_seq=1 ttl=251 time=39.2 ms
64 bytes from 14.116.220.47 (14.116.220.47): icmp_seq=2 ttl=251 time=39.2 ms
64 bytes from 14.116.220.47 (14.116.220.47): icmp_seq=3 ttl=251 time=39.2 ms
64 bytes from 14.116.220.47 (14.116.220.47): icmp_seq=4 ttl=251 time=39.2 ms

--- cloud189.21cn.com ping statistics ---
4 packets transmitted, 4 received, 0% packet loss, time 1516ms
rtt min/avg/max/mdev = 36.021/36.031/36.054/0.013 ms`
  },
  {
    name: '上海节点 - cloud.189.cn',
    rawOutput: `PING cloud189.21cn.com (14.18.110.142) 56(84) bytes of data.
64 bytes from 14.18.110.142 (14.18.110.142): icmp_seq=1 ttl=34 time=5.69 ms
64 bytes from 14.18.110.142 (14.18.110.142): icmp_seq=2 ttl=34 time=5.75 ms
64 bytes from 14.18.110.142 (14.18.110.142): icmp_seq=3 ttl=34 time=5.73 ms
64 bytes from 14.18.110.142 (14.18.110.142): icmp_seq=4 ttl=34 time=5.71 ms

--- cloud189.21cn.com ping statistics ---
4 packets transmitted, 4 received, 0% packet loss, time 1502ms
rtt min/avg/max/mdev = 31.428/31.497/31.647/0.087 ms`
  },
  {
    name: 'wobshare.us.kg节点',
    rawOutput: `PING wobshare.us.kg (76.76.21.21) 56(84) bytes of data.
64 bytes from 76.76.21.21 (76.76.21.21): icmp_seq=1 ttl=251 time=86.8 ms
64 bytes from 76.76.21.21 (76.76.21.21): icmp_seq=2 ttl=251 time=95.1 ms
64 bytes from 76.76.21.21 (76.76.21.21): icmp_seq=3 ttl=251 time=97.1 ms
64 bytes from 76.76.21.21 (76.76.21.21): icmp_seq=4 ttl=251 time=96.4 ms

--- wobshare.us.kg ping statistics ---
4 packets transmitted, 4 received, 0% packet loss, time 1503ms
rtt min/avg/max/mdev = 86.8/93.85/97.1/4.2 ms`
  }
];

console.log('🧪 测试rawOutput延迟提取功能\n');

testCases.forEach((testCase, index) => {
  console.log(`\n${index + 1}. 测试: ${testCase.name}`);
  console.log('='.repeat(50));
  
  const result = extractRealLatencyFromRawOutput(testCase.rawOutput);
  
  console.log(`\n结果: ${result}ms`);
  console.log('='.repeat(50));
});
